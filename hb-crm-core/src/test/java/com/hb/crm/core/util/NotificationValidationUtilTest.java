package com.hb.crm.core.util;

import com.hb.crm.core.Enums.DisableNotificationType;
import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.beans.Notification.NotificationDisableSetting;
import com.hb.crm.core.beans.Notification.NotificationMuteUser;
import com.hb.crm.core.beans.Notification.NotificationSetting;
import com.hb.crm.core.beans.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for NotificationValidationUtil
 */
class NotificationValidationUtilTest {

    private NotificationSetting notificationSetting;
    private NotificationMuteUser notificationMuteUser;
    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId("user123");
        
        notificationSetting = new NotificationSetting(testUser);
        notificationSetting.setDisabledNotifications(new ArrayList<>());

        notificationMuteUser = new NotificationMuteUser("231", testUser);
        notificationMuteUser.setMuteDisableSetting(new ArrayList<>());
    }

    @Test
    void testIsNotificationTypeDisabledForChannel_WithDisabledChannel_ReturnsTrue() {
        NotificationDisableSetting disableSetting = new NotificationDisableSetting(
            DisableNotificationType.POST_NOTIFICATIONS,
            Arrays.asList(NotificationChannelType.Push, NotificationChannelType.Email)
        );
        notificationSetting.getDisabledNotifications().add(disableSetting);

        // Test that push notifications are disabled for posts
        assertTrue(NotificationValidationUtil.isNotificationTypeDisabledForChannel(
            NotificationType.NewPostFromFollowedInfluencer,
            NotificationChannelType.Push,
            notificationSetting.getDisabledNotifications()));

        // Test that email notifications are disabled for posts
        assertTrue(NotificationValidationUtil.isNotificationTypeDisabledForChannel(
            NotificationType.NewPostFromFollowedInfluencer,
            NotificationChannelType.Email,
            notificationSetting.getDisabledNotifications()));

        // Test that SMS notifications are not disabled for posts
        assertFalse(NotificationValidationUtil.isNotificationTypeDisabledForChannel(
            NotificationType.NewPostFromFollowedInfluencer,
            NotificationChannelType.Sms,
            notificationSetting.getDisabledNotifications()));
    }

    @Test
    void testIsNotificationBlockedForChannel_WithDisabledChannel_ReturnsTrue() {
        NotificationDisableSetting disableSetting = new NotificationDisableSetting(
            DisableNotificationType.POST_NOTIFICATIONS,
            Arrays.asList(NotificationChannelType.Push)
        );
        notificationSetting.getDisabledNotifications().add(disableSetting);

        // Test that notification is blocked for disabled channel
        assertTrue(NotificationValidationUtil.isNotificationBlockedForChannel(
            NotificationType.NewPostFromFollowedInfluencer,
            NotificationChannelType.Push,
            notificationSetting,
            null,
                null
        ));

        // Test that notification is not blocked for enabled channel
        assertFalse(NotificationValidationUtil.isNotificationBlockedForChannel(
            NotificationType.NewPostFromFollowedInfluencer,
            NotificationChannelType.Email,
            notificationSetting,
            null,
                null
        ));
    }
}
