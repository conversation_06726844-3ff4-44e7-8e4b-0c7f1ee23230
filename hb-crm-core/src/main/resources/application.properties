spring.mail.host=smtp.office365.com
spring.mail.port=587
spring.mail.username=<PERSON><PERSON><PERSON>@havebreak.com
spring.mail.password=Ruz01282
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=true

twilio.accountSid=**********************************
twilio.authToken=a6ba5eeb95085d09c6b7140cbdadd769
twilio.phoneNumber=+***********

cloud.aws.credentials.secretKey=mv5lcgPvykbN4OI0SggzU6nIJ7ocrkT9xMxIzzqO
cloud.aws.credentials.accessKey=********************
cloud.aws.region.static=eu-central-1
cloud.aws.bucketName=dev-media-havebreak-com
cloud.aws.ivs.ChannelType=ADVANCED_HD
cloud.aws.ivs.LatencyMode=LOW           
cloud.aws.ivs.AwsAccountId=************
cloud.aws.ivs.recordingConfigurationArn=arn:aws:ivs:eu-central-1:************:recording-configuration/2fBWkmGGot2d