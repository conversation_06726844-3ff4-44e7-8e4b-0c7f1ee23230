package com.hb.crm.core.services;
import com.hb.crm.core.beans.LiveStream.LiveStreamConfiguration;
import com.hb.crm.core.exceptions.DataNotFoundException;
import com.hb.crm.core.repositories.LiveStream.LiveStreamConfigurationRepository;
import com.hb.crm.core.services.interfaces.LiveStreamConfigurationService;

import lombok.RequiredArgsConstructor;

import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
@Service
@RequiredArgsConstructor

public class LiveStreamConfigurationServiceImpl implements LiveStreamConfigurationService {
    private static final int ALL_LIMIT = 99999;
    private final LiveStreamConfigurationRepository liveStreamConfigurationRepository;

    @Override
    public Page<LiveStreamConfiguration> getPagedConfigurations( int page, int limit) {
        if (page < 0) {
            limit = ALL_LIMIT;
            page = 0;
        }
        final Pageable pageable = PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<LiveStreamConfiguration> configurationPage = liveStreamConfigurationRepository.findAll(pageable);      
        
        return configurationPage;
    }
   
    @Override
    @Transactional
    public LiveStreamConfiguration createConfiguration(LiveStreamConfiguration configuration) {
        
        configuration.setCreatedAt(LocalDateTime.now());
        configuration.setUpdatedAt(LocalDateTime.now());
        
        // Deactivate all existing configurations first
        deactivateAllConfigurations();
        
        configuration.setActive(true);
        
        LiveStreamConfiguration savedConfiguration = liveStreamConfigurationRepository.save(configuration);        
        return savedConfiguration;
    }

    @Override
    @Transactional
    public LiveStreamConfiguration activateConfiguration(String id) {
    
        // Find the configuration to activate
        Optional<LiveStreamConfiguration> configOptional = liveStreamConfigurationRepository.findById(id);
        if (configOptional.isEmpty()) {
            throw new IllegalArgumentException("Live Configuration not found with id: " + id);
        }
        
        LiveStreamConfiguration configurationToActivate = configOptional.get();
        
        // Check if it's already active
        if (configurationToActivate.isActive()) {
            return configurationToActivate;
        }
        
        // Deactivate all configurations first (only one can be active)
        deactivateAllConfigurations();
        
        // Activate the specified configuration
        configurationToActivate.setActive(true);
        configurationToActivate.setUpdatedAt(LocalDateTime.now());
        
        LiveStreamConfiguration activatedConfiguration = liveStreamConfigurationRepository.save(configurationToActivate);
        
        return activatedConfiguration;
    }

    @Override
    @Transactional
    public LiveStreamConfiguration deactivateConfiguration(String id) {
        
        // Find the configuration to deactivate
        Optional<LiveStreamConfiguration> configOptional = liveStreamConfigurationRepository.findById(id);
        if (configOptional.isEmpty()) {
            throw new IllegalArgumentException("Live Stream Configuration not found with id: " + id);
        }
        
        LiveStreamConfiguration configurationToDeactivate = configOptional.get();
        
        // Check if it's already inactive
        if (!configurationToDeactivate.isActive()) {
            return configurationToDeactivate;
        }
        
        // Deactivate the configuration
        configurationToDeactivate.setActive(false);
        configurationToDeactivate.setUpdatedAt(LocalDateTime.now());
        
        LiveStreamConfiguration deactivatedConfiguration = liveStreamConfigurationRepository.save(configurationToDeactivate);
        
        return deactivatedConfiguration;
    }

    @Override
    public LiveStreamConfiguration getActiveConfiguration() 
    {
        try 
        {
            Optional<LiveStreamConfiguration> activeConfig = liveStreamConfigurationRepository.findActiveConfiguration();
            if (activeConfig.isEmpty()) 
            {
                throw new DataNotFoundException("No active Live Stream configuration found.");
            }
            LiveStreamConfiguration config = activeConfig.get();
            return config;
        } 
        catch (DataNotFoundException e) 
        {
            throw e;
        }
        catch (Exception e) {
            throw new RuntimeException("Failed to fetch active configuration", e);
        }
    }

    private void deactivateAllConfigurations() {
    List<LiveStreamConfiguration> activeConfigurations = liveStreamConfigurationRepository.findAllActiveConfigurations();
    for (LiveStreamConfiguration config : activeConfigurations) {
        config.setActive(false);
        config.setUpdatedAt(LocalDateTime.now());
    }
    if (!activeConfigurations.isEmpty()) {
        liveStreamConfigurationRepository.saveAll(activeConfigurations);
    }
}
}