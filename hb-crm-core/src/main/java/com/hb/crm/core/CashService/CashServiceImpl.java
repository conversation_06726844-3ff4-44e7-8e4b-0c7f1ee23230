package com.hb.crm.core.CashService;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.hb.crm.core.beans.FlightAndHotels;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;
@Service
public class CashServiceImpl implements CashService {

    private static final String USER_FCM_TOKEN_PREFIX = "user_fcm_token:";
    private static final String FLIGHT_HOTELS_PREFIX = "flight_hotels:";
    private static final String FLIGHT_HOTELS_IDS_KEY = "flight_hotels_ids";
    private static final String CACHE_NAME = "myCache";

    @Value("${redis.active}")
    private boolean redIsActive;

    @Value("${node.cache.proxy.url}")
    private String nodeCacheUrl;

    @Value("${node.cache.token}")
    private String nodeCacheToken;

    private final CacheManager cacheManager;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public CashServiceImpl(CacheManager cacheManager) {
        this.cacheManager = cacheManager;
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private void writeToCache(String key, Object value, long expirationMillis) {
        if (redIsActive) 
        {
            Map<String, Object> body = new HashMap<>();
            body.put("key", key);
            body.put("value", value);
            body.put("ttl", expirationMillis);

            HttpHeaders headers = new HttpHeaders();
            //headers.set("x-access-token", nodeCacheToken);

            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);

            try 
            {

                String setUrl=nodeCacheUrl+"/cache/set";
                restTemplate.postForEntity(setUrl, request, Void.class);
            } catch (Exception e) {
                System.err.println("Failed to write to Node.js cache proxy: " + e.getMessage());
            }
        } 
        else 
        {
            Cache cache = cacheManager.getCache(CACHE_NAME);
            if (cache != null) {
                cache.put(key, value);
                cache.put(key + "_expiration", System.currentTimeMillis() + expirationMillis);
            }
        }
    }

    @SuppressWarnings("unchecked")
    private <T> T readFromCache(String key, Class<T> clazz) 
    {
        if(key.contains("user_fcm_token"))return null;
        if (redIsActive)
        {
            try 
            {
                HttpHeaders headers = new HttpHeaders();
                //headers.set("x-access-token", nodeCacheToken);
    
                headers.setContentType(MediaType.APPLICATION_JSON);
                String getUrl = nodeCacheUrl + "/cache/get?key=" + key;
                HttpEntity<Void> request = new HttpEntity<>(headers);
                
                // Get response as String to handle JSON parsing
                ResponseEntity<String> response = restTemplate.exchange(
                        getUrl,
                        HttpMethod.GET,
                        request,
                        String.class
                );
            
                String responseBody = response.getBody();
                if (responseBody == null || responseBody.trim().isEmpty()) 
                {
                    return null;
                }
                
                // Parse the Redis proxy response: {"key": "...", "value": {...}}
                try 
                {
                    JsonNode jsonNode = objectMapper.readTree(responseBody);
                    
                    // Check if response has the expected "value" field
                    if (jsonNode.has("value")) 
                    {
                        JsonNode valueNode = jsonNode.get("value");
                        
                        // Check if value is null or empty
                        if (valueNode == null || valueNode.isNull() || valueNode.isMissingNode()) 
                        {
                            return null;
                        }
                        
                        // Deserialize the value to the target class
                        if (clazz == Object.class) 
                        {
                            return (T) objectMapper.treeToValue(valueNode, Object.class);
                        }
                        return objectMapper.treeToValue(valueNode, clazz);
                    } 
                    else 
                    {
                        // Fallback: try to parse entire response as target class
                        return objectMapper.readValue(responseBody, clazz);
                    }
                } 
                catch (Exception parseException) 
                {
                    System.err.println("Failed to parse Redis response: " + parseException.getMessage());
                    System.err.println("Response body: " + responseBody);
                    return null;
                }
            
            } 
            catch (HttpClientErrorException.NotFound e) 
            {
                return null;
            } 
            catch (Exception e) 
            {
                System.err.println("Failed to read from Node.js cache proxy: " + e.getMessage());
                return null;
            }
        }
        else 
        {
            Cache cache = cacheManager.getCache(CACHE_NAME);
            if (cache == null) return null;

            Cache.ValueWrapper wrapper = cache.get(key);
            if (wrapper == null) return null;

            Cache.ValueWrapper expirationWrapper = cache.get(key + "_expiration");
            if (expirationWrapper != null) 
            {
                Long expirationTime = (Long) expirationWrapper.get();
                if (expirationTime < System.currentTimeMillis()) {
                    cache.evict(key);
                    cache.evict(key + "_expiration");
                    return null;
                }
            }

            Object value = wrapper.get();
            if (value == null) 
            {
                return null;
            }
            
            if (clazz.isInstance(value))
            {
                return clazz.cast(value);
            }

            return null;
        }
    }

    private void deleteFromCache(String key) 
    {
        if (redIsActive) 
        {
            try 
            {
                Map<String, String> body = new HashMap<>();
                body.put("key", key);
                HttpHeaders headers = new HttpHeaders();
                //headers.set("x-access-token", nodeCacheToken);
    
                headers.setContentType(MediaType.APPLICATION_JSON);
    
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(headers);
                restTemplate.postForEntity(nodeCacheUrl + "/cache/delete", request, Void.class);
            } 
            catch (Exception e) 
            {
                System.err.println("Failed to delete from Redis proxy: " + e.getMessage());
            }
        } else {
            Cache cache = cacheManager.getCache(CACHE_NAME);
            if (cache != null) {
                cache.evict(key);
                cache.evict(key + "_expiration");
            }
        }
    }
    // private HttpHeaders createHeaders() {
    //     HttpHeaders headers = new HttpHeaders();
    //     headers.setContentType(MediaType.APPLICATION_JSON);
    //     headers.set("x-access-token", nodeCacheToken);
    //     return headers;
    // }

    public void storeData(String key, Object value, Long lifetimeMillis) 
    {
        long expiration = (lifetimeMillis != null) ? lifetimeMillis : TimeUnit.MINUTES.toMillis(40);
        writeToCache(key, value, expiration);
    }

    public void storeData(String key, UserClaimResult value, Long lifetimeMillis) 
    {
        long expiration = (lifetimeMillis != null) ? lifetimeMillis : TimeUnit.MINUTES.toMillis(40);
        writeToCache(key, value, expiration);
        writeToCache(value.getUserid(), value, expiration);
    }

    public void deleteData(String key) 
    {
        deleteFromCache(key);
    }

    public Object retrieveData(String key) 
    {
        return readFromCache(key, Object.class);
    }

    public UserClaimResult retrieveUserData(String key) 
    {
        return readFromCache(key, UserClaimResult.class);
    }

    /* //////////////////////// Flights and Hotels Caching ////////////////////// */

    public void storeFlightAndHotels(FlightAndHotels flightAndHotels, Long lifetimeMillis)
    {
        String key = FLIGHT_HOTELS_PREFIX + flightAndHotels.getId();
        long expiration = (lifetimeMillis != null) ? lifetimeMillis : TimeUnit.MINUTES.toMillis(40);

        writeToCache(key, flightAndHotels, expiration);

        if (redIsActive) 
        {
            try 
            {
                Map<String, Object> body = new HashMap<>();
                body.put("key", FLIGHT_HOTELS_IDS_KEY);
                body.put("value", flightAndHotels.getId());
                HttpHeaders headers = new HttpHeaders();
                //headers.set("x-access-token", nodeCacheToken);
    
                headers.setContentType(MediaType.APPLICATION_JSON);
    
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
                restTemplate.postForEntity(nodeCacheUrl + "/cache/set", request, Void.class); // assumes your proxy supports sadd
            } 
            catch (Exception e) 
            {
                System.err.println("Failed to update Redis ID set: " + e.getMessage());
            }
        }
        else 
        {
            Cache cache = cacheManager.getCache(CACHE_NAME);
            Set<String> ids = getStoredIds(cache);
            ids.add(flightAndHotels.getId());
            cache.put(FLIGHT_HOTELS_IDS_KEY, ids);
        }
        // String key = FLIGHT_HOTELS_PREFIX + flightAndHotels.getId();
        // long expiration = (lifetimeMillis != null) ? lifetimeMillis : TimeUnit.MINUTES.toMillis(40);

        // writeToCache(key, flightAndHotels, expiration);

        // if (!redIsActive) 
        // {
        //     Cache cache = cacheManager.getCache(CACHE_NAME);
        //     Set<String> ids = getStoredIds(cache);
        //     ids.add(flightAndHotels.getId());
        //     cache.put(FLIGHT_HOTELS_IDS_KEY, ids);
        // }
    }

    public FlightAndHotels getFlightAndHotelsById(String id) 
    {
        return readFromCache(FLIGHT_HOTELS_PREFIX + id, FlightAndHotels.class);
    }

    public List<FlightAndHotels> getAllFlightAndHotels()
    {
        Set<String> ids;

        if (redIsActive) 
        {
            ids = getStoredIds(null); // Redis mode
        } 
        else 
        {
            Cache cache = cacheManager.getCache(CACHE_NAME);
            ids = getStoredIds(cache); // Local mode
        }
    
        List<FlightAndHotels> result = new ArrayList<>();
        Iterator<String> iterator = ids.iterator();
    
        while (iterator.hasNext()) 
        {
            String id = iterator.next();
            FlightAndHotels item = getFlightAndHotelsById(id);
            if (item != null) 
            {
                result.add(item);
            } 
            else 
            {
                iterator.remove(); // Clean up invalid IDs
            }
        }
    
        // Only update local cache
        if (!redIsActive) 
        {
            Cache cache = cacheManager.getCache(CACHE_NAME);
            cache.put(FLIGHT_HOTELS_IDS_KEY, ids);
        } 
        
    
        return result;
        // if (redIsActive) 
        // {
        //     throw new UnsupportedOperationException("ID list handling for Redis proxy is not implemented");
        // }

        // Cache cache = cacheManager.getCache(CACHE_NAME);
        // Set<String> ids = getStoredIds(cache);
        // List<FlightAndHotels> result = new ArrayList<>();

        // Iterator<String> iterator = ids.iterator();
        // while (iterator.hasNext()) 
        // {
        //     String id = iterator.next();
        //     FlightAndHotels item = getFlightAndHotelsById(id);
        //     if (item != null) 
        //     {
        //         result.add(item);
        //     } 
        //     else 
        //     {
        //         iterator.remove();
        //     }
        // }

        // cache.put(FLIGHT_HOTELS_IDS_KEY, ids);
        // return result;
    }

    public void deleteFlightAndHotels(String id) 
    {
        String key = FLIGHT_HOTELS_PREFIX + id;
        deleteFromCache(key);
    
        if (!redIsActive) 
        {
            Cache cache = cacheManager.getCache(CACHE_NAME);
            Set<String> ids = getStoredIds(cache);
            ids.remove(id);
            cache.put(FLIGHT_HOTELS_IDS_KEY, ids);
        } 
        
        else 
        {
            try 
            {
                // 1. Get current ID list
                Set<String> ids = getStoredIds(null);
                ids.remove(id);
    
                // 2. Push updated list back to Redis
                Map<String, Object> body = new HashMap<>();
                body.put("key", FLIGHT_HOTELS_IDS_KEY);
                body.put("value", ids);
                body.put("ttl", 3600000); // Optional TTL (1 hour)
    
                HttpHeaders headers = new HttpHeaders();
                //headers.set("x-access-token", nodeCacheToken);
    
                headers.setContentType(MediaType.APPLICATION_JSON);
    
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
                restTemplate.postForEntity(nodeCacheUrl + "/cache/set", request, String.class);
            } 
            catch (Exception e) 
            {
                System.err.println("Failed to update ID list in Redis: " + e.getMessage());
            }
        }
        // String key = FLIGHT_HOTELS_PREFIX + id;
        // deleteFromCache(key);

        // if (!redIsActive) {
        //     Cache cache = cacheManager.getCache(CACHE_NAME);
        //     Set<String> ids = getStoredIds(cache);
        //     ids.remove(id);
        //     cache.put(FLIGHT_HOTELS_IDS_KEY, ids);
        // }
    }

    @SuppressWarnings("unchecked")
    private Set<String> getStoredIds(Cache cache)
    {
        if (redIsActive) 
        {
            try 
            {
                HttpHeaders headers = new HttpHeaders();
                //headers.set("x-access-token", nodeCacheToken);
    
                headers.setContentType(MediaType.APPLICATION_JSON);
    
                HttpEntity<Map<String, Object>> request = new HttpEntity<>(headers);
                ResponseEntity<String> response = restTemplate.exchange(
                    nodeCacheUrl + "/cache/get?key=" + FLIGHT_HOTELS_IDS_KEY,
                    HttpMethod.GET,
                    request,
                    String.class
                );
    
                String responseBody = response.getBody();
                if (responseBody == null || responseBody.trim().isEmpty()) 
                {
                    return new HashSet<>();
                }
    
                JsonNode root = objectMapper.readTree(responseBody);
                if (root.has("value") && root.get("value").isArray()) {
                    Set<String> result = new HashSet<>();
                    for (JsonNode node : root.get("value")) 
                    {
                        result.add(node.asText());
                    }
                    return result;
                } 
                else 
                {
                    return new HashSet<>();
                }
            } 
            catch (Exception e) 
            {
                System.err.println("Failed to get stored IDs from Redis: " + e.getMessage());
                return new HashSet<>();
            }
        } 
        else 
        {
            if (cache == null) return new HashSet<>();
            Cache.ValueWrapper wrapper = cache.get(FLIGHT_HOTELS_IDS_KEY);
            return (wrapper != null && wrapper.get() instanceof Set)
                    ? (Set<String>) wrapper.get()
                    : new HashSet<>();
        }
        // if (cache == null) return new HashSet<>();
        // Cache.ValueWrapper wrapper = cache.get(FLIGHT_HOTELS_IDS_KEY);
        // return (wrapper != null && wrapper.get() instanceof Set)
        //         ? (Set<String>) wrapper.get()
        //         : new HashSet<>();
    }

    public void storeUserFCMToken(String userId, String fcmToken)
    {
        String key = USER_FCM_TOKEN_PREFIX + userId;
        writeToCache(key, fcmToken, TimeUnit.DAYS.toMillis(366));
    }

    public String getUserFcmToken(String userId) 
    {
        String key = USER_FCM_TOKEN_PREFIX + userId;
        return readFromCache(key, String.class);
    }

    public void deleteUserFCMToken(String userId) {
        String key = USER_FCM_TOKEN_PREFIX + userId;
        deleteFromCache(key);
    }
}
