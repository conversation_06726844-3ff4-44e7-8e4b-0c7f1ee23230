package com.hb.crm.core.repositories.LiveStream;

import com.hb.crm.core.beans.LiveStream.LiveStreamComment;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LiveStreamCommentRepository extends MongoRepository<LiveStreamComment, String> {

    @Query("{'liveStream.$id': ?0}")
    Page<LiveStreamComment> findByLiveStreamId(ObjectId liveStreamId, Pageable pageable);

    @Query("{'liveStream.$id': ?0}")
    List<LiveStreamComment> findByLiveStreamId(ObjectId liveStreamId);

    @Query("{'liveStream.$id': ?0, 'user.$id': ?1}")
    List<LiveStreamComment> findByLiveStreamIdAndUserId(ObjectId liveStreamId, ObjectId userId);

    @Query("{'user.$id': ?0}")
    List<LiveStreamComment> findByUserId(ObjectId userId);

    @Query("{'liveStream.$id': ?0}")
    long countByLiveStreamId(ObjectId liveStreamId);
} 