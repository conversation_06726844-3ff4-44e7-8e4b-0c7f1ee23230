package com.hb.crm.core.Enums;

public enum NotificationType {

    // Packages
    MatchedMoodsNewPackage,
    FollowedInfluencerNewPackage,
    InfluencerNewPackage,
    InfluencerUpdatePackage,
    PackageCapacitySubscribedUsers,
    PackageCapacityFavouriteUsers,


    // Posts Reels & Stories
    NewStoryFromFollowedInfluencer,
    NewPostFromFollowedInfluencer,
    NewPostFromFavouritePackageUser,
    NewStoryFromFavouritePackageUser,



    // Chats
    ChatMessage,
    ChatMessageVideo,
    ChatMessageAudio,
    ChatMessageFile,
    ChatMessagePoll,
    ChatMessageImage,

    GroupChatMessage,
    GroupChatMessageVideo,
    GroupChatMessageAudio,
    GroupChatMessageFile,
    GroupChatMessagePoll,
    GroupChatMessageImage,



    // Interactions
    CommentedOnLikedPost,
    CommentedOnMyPost,
    ReactOnLikedPost,
    ReactOnLikedStory,
    ReactOnMyComment,
    ReactOnMyPost,
    ReactOnMyReel,
    ReactOnMyStory,
    ReplyComment,





    SubmittedSubscribe,
    SubscriptionCanceled,
    Follow,
    PaymentSucceeded,
    AcceptPackage,
    RejectPackage,
    PostPackage,


    // register
    SuccessLoginAdmin,
    FailedLoginAdmin,
    SuccessPasswordChange,
    FailedPasswordChange,
    LiveStarted,




}
