package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Country;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CountryRepository extends MongoRepository<Country, String> {
    List<Country> findByNameIn(List<String> countryNames);
    @Query("{ '$or': [ { 'airportSynced': false }, { 'airportSynced': { '$exists': false } } ] }")
    List<Country> findByAirportSyncedFalseOrNull();

    @Query("{ 'name': { $regex: ?0, $options: 'i' }}")
    List<Country> findByNameRegex(String searchTerm);

}
