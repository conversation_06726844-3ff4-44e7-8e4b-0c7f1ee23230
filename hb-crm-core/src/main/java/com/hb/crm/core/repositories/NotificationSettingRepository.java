package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.NotificationSetting;
import com.hb.crm.core.beans.User;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NotificationSettingRepository extends MongoRepository<NotificationSetting, String> {
    List<NotificationSetting> findByUser(User user);
    NotificationSetting findByUser_Id(String userId);
}
