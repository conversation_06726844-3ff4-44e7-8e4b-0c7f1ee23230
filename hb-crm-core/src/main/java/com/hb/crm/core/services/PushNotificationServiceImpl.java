package com.hb.crm.core.services;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import com.hb.crm.core.beans.Notification.CrmNotification;
import com.hb.crm.core.services.interfaces.PushNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class PushNotificationServiceImpl implements PushNotificationService {

    @Override
    public void send(List<CrmNotification> notifications) {
        for (CrmNotification notification : notifications) {
            send(notification);
        }
    }

    @Override
    public void send(CrmNotification notification) {

        if (!notification.getTopic().isEmpty()) {
            if (notification.getPayload() != null) {
                sendBroadcast(notification.getTopic(), notification.getTitle(), notification.getBody(), notification.getPayload());

            } else {
                sendBroadcast(notification.getTopic(), notification.getTitle(), notification.getBody());
            }
        }

        if (notification.getUser() != null) {
            if (notification.getPayload() != null) {
                send(notification.getUser().getToken(), notification.getTitle(), notification.getBody(), notification.getPayload());
            } else {
                send(notification.getUser().getToken(), notification.getTitle(), notification.getBody());
            }
        }


    }

    @Override
    public void send(String token, String title, String body) {
        send(token, title, body, null);
    }

    @Override
    public void send(String token, String title, String body, Map<String, String> payload) {
        try {
            var messageBuilder = Message.builder()
                    .setToken(token)
                    .setNotification(buildNotification(title, body));

            if (payload != null) {
                messageBuilder.putAllData(payload);
            }
            FirebaseMessaging.getInstance().send(messageBuilder.build());
        } catch (Exception e) {
            log.error("Error while sending notification", e);
        }
    }

    @Override
    public void sendBroadcast(String topic, String title, String body) {
        sendBroadcast(topic, title, body, null);
    }

    @Override
    public void sendBroadcast(String topic, String title, String body, Map<String, String> payload) {
        try {
            var messageBuilder = Message.builder()
                    .setTopic(topic)
                    .setNotification(buildNotification(title, body));

            if (payload != null) {
                messageBuilder.putAllData(payload);
            }
            FirebaseMessaging.getInstance().send(messageBuilder.build());
        } catch (Exception e) {
            log.error("Error while sending notification", e);
        }
    }

    private Notification buildNotification(String title, String body) {
        Notification.Builder builder = Notification.builder();
        builder.setTitle(title);
        builder.setBody(body);
        return builder.build();
    }


}
