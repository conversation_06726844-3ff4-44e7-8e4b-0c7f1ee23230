package com.hb.crm.core.searchRepositories;

import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.Media;
import com.hb.crm.core.dtos.searchUserResultDto;
import com.hb.crm.core.searchBeans.SearchUser;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SearchUserRepository extends MongoRepository<SearchUser, String> {

    List<SearchUser> findByIdIn(List<String> ids);
    /**
     * Performs real-time autocomplete search for users based on first name, last name, and about section.
     * 
     * @param query The search string to match against user details
     * @return List of matching SearchUser objects
     */
    @Aggregation(pipeline = {
            "{",
            "    $search: {",
            "        'index': 'autocomplete',",
            "        'compound': {",
            "            'should': [",
            "                { 'autocomplete': { 'query': ?0, 'path': 'firstName' } },",
            "                { 'autocomplete': { 'query': ?0, 'path': 'lastName' } },",
            "                { 'autocomplete': { 'query': ?0, 'path': 'about' } }",
            "            ],",
            "            'minimumShouldMatch': 1",
            "        }",
            "    }",
            "}"
    })
    List<SearchUser> search(String query);


    /**
     * Retrieves paginated users by their ObjectIds.
     * 
     * @param ids List of user ObjectIds to fetch
     * @param skip Number of records to skip (pagination offset)
     * @param limit Maximum number of records to return
     * @return SearchUserResultDto containing paginated results and total count
     */
    @Aggregation(pipeline = {
            // Match stage instead of search for single ID case
            "{ $match: { '_id': { $in: ?0 } } }",
            // Simple facet for pagination
            "{" +
            "    $facet: {" +
            "        filteredResults: [ { $skip: ?1 }, { $limit: ?2 } ]," +
            "        totalCount: [ { $count: 'total' } ]" +
            "    }" +
            "}",
            // Simplified project stage
            "{" +
            "    $project: {" +
            "        filteredResults: 1," +
            "        totalCount: { $ifNull: [{ $arrayElemAt: ['$totalCount.total', 0] }, 0] }" +
            "    }" +
            "}"
    })
    searchUserResultDto findByIdIn(List<ObjectId> ids, int skip, int limit);

    /**
     * Searches and retrieves paginated users by their IDs and a search term.
     * 
     * @param search Search term to filter users
     * @param ids List of user ObjectIds to search within
     * @param skip Number of records to skip (pagination offset)
     * @param limit Maximum number of records to return
     * @return SearchUserResultDto containing filtered results and total count
     */
    @Aggregation(pipeline = {
            // Simple match stage with regex for search
            "{ $match: { " +
            "    '_id': { $in: ?1 }," +
            "    $or: [" +
            "        { 'firstName': { $regex: ?0, $options: 'i' } }," +
            "        { 'lastName': { $regex: ?0, $options: 'i' } }" +
            "    ]" +
            "} }",
            // Simple facet for pagination
            "{" +
            "    $facet: {" +
            "        filteredResults: [ { $skip: ?2 }, { $limit: ?3 } ]," +
            "        totalCount: [ { $count: 'total' } ]" +
            "    }" +
            "}",
            // Simplified project stage
            "{" +
            "    $project: {" +
            "        filteredResults: 1," +
            "        totalCount: { $ifNull: [{ $arrayElemAt: ['$totalCount.total', 0] }, 0] }" +
            "    }" +
            "}"
    })
    searchUserResultDto findByIdInAndNameContainingIgnoreCase(String search, List<ObjectId> ids, int skip, int limit);

    /**
     * Performs a comprehensive search across users and their associated packages.
     * This method combines results from direct user matches and matches in package names,
     * removing duplicates and providing paginated results.
     * 
     * @param query Search term to match against usernames and package names
     * @param skip Number of records to skip (pagination offset)
     * @param limit Maximum number of records to return
     * @return SearchUserResultDto containing combined search results and total count
     */
    @Aggregation(pipeline = {
            // First stage: Search users by name
            "{",
            "    $search: {",
            "        'index': 'default',",
            "        'compound': {",
            "            'should': [",
            "                { 'wildcard': { 'path': 'firstName', 'query': '*?0*', 'allowAnalyzedField': true } },",
            "                { 'wildcard': { 'path': 'lastName', 'query': '*?0*', 'allowAnalyzedField': true } }",
            "            ]",
            "        }",
            "    }",
            "}",

            // Union with package search results
            "{",
            "    $unionWith: {",
            "        coll: 'search',",
            "        pipeline: [",
            "            {",
            "                $search: {",
            "                    'index': 'default',",
            "                    'compound': {",
            "                        'must': [",
            "                            { 'equals': { 'path': '_class', 'value': 'com.hb.crm.core.searchBeans.searchPackage' } },",
            "                            { 'wildcard': { 'path': 'name', 'query': '*?0*', 'allowAnalyzedField': true } }",
            "                        ]",
            "                    }",
            "                }",
            "            },",
            "            { $replaceRoot: { newRoot: '$infulancer' } },",
            "            {",
            "                $lookup: {",
            "                    from: 'searchUser',",
            "                    localField: '_id',",
            "                    foreignField: '_id',",
            "                    as: 'completeUser'",
            "                }",
            "            },",
            "            { $unwind: { path: '$completeUser', preserveNullAndEmptyArrays: false } },",
            "            { $replaceRoot: { newRoot: '$completeUser' } }",
            "        ]",
            "    }",
            "}",

            // Remove duplicate results
            "{",
            "    $group: {",
            "        '_id': '$_id',",
            "        'doc': { $first: '$$ROOT' }",
            "    }",
            "}",

            // Restore document structure
            "{ $replaceRoot: { newRoot: '$doc' } }",

            // Pagination facet
            "{",
            "    $facet: {",
            "        filteredResults: [ { $skip: ?1 }, { $limit: ?2 } ],",
            "        totalCount: [ { $count: 'total' } ]",
            "    }",
            "}",

            // Final projection with null handling
            "{",
            "    $project: {",
            "        filteredResults: 1,",
            "        totalCount: {",
            "            $cond: {",
            "                if: { $eq: [ { $size: '$totalCount' }, 0 ] },",
            "                then: 0,",
            "                else: { $arrayElemAt: [ '$totalCount.total', 0 ] }",
            "            }",
            "        }",
            "    }",
            "}"
    })
    searchUserResultDto searchByNameOrPackage(String query, int skip, int limit);


    @Aggregation(pipeline = {
            "{ $search: { " +
                    "'index': 'default', " +
                    "'compound': { " +
                    "'should': [ " +
                    "{ 'autocomplete': { 'query': ?0, 'path': 'firstName' } }," +
                    "{ 'autocomplete': { 'query': ?0, 'path': 'lastName' } }," +
                    "{ 'autocomplete': { 'query': ?0, 'path': 'about' } }" +
                    "], " +
                    "'minimumShouldMatch': 1 " +
                    "} } }",

            // Convert _id to string and trim spaces
            "{ $addFields: { '_id': { '$trim': { 'input': { '$toString': '$_id' } } } } }",

            // Lookup media by userId
            "{ $lookup: { " +
                    "'from': 'media', " +
                    "'localField': '_id', " +
                    "'foreignField': 'userId', " +
                    "'as': 'relatedMedia' " +
                    "} }",

            // Unwind the media array
            "{ $unwind: '$relatedMedia' }",

            // Replace the root with the media object itself
            "{ $replaceRoot: { 'newRoot': '$relatedMedia' } }"
    })
    List<Media> findAllMediasByUserName(String query);
    Page<SearchUser> findByUsertypeIn(List<UserType> usertypes, Pageable pageable);

}
