package com.hb.crm.core.searchBeans;

import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.Mood;
import com.hb.crm.core.beans.User;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.modelmapper.ModelMapper;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Setter
@Getter
@Document
@NoArgsConstructor
public class SearchUser implements Serializable {
    @Id
    private String id;
    @CreatedDate
    private LocalDateTime created;
    @LastModifiedDate
    private LocalDateTime updated;
    private String about;
    private String firstName;
    private String lastName;
    private String username;
    private String email;
    private String guestEmail;
    private String mobile;
    private String city;
    private String country;
    private List<MediaWrapperSearch> medias;
    private int follwerscount;
    private int followingcount;
    private String profileImage;
    private String coverImage;
    private Gender gender;
    private List<searchPost> stories;
    private UserType usertype;
    private List<Mood> moods;
    // Constructor to initialize SearchUser from User
    public SearchUser(User user) {
        ModelMapper modelMapper = new ModelMapper();
        this.id = user.getId();
        this.about = user.getAbout();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.username = user.getUsername();
        this.guestEmail = user.getGuestEmail();
        this.city = user.getCity();
        this.country = user.getCountry();
        this.medias = user.getMedias()!=null ? user.getMedias().stream().map(z->modelMapper.map(z, MediaWrapperSearch.class)).collect(Collectors.toList()) : new ArrayList<>();
        this.follwerscount = user.getFollwerscount();
        this.followingcount = user.getFollowingcount();
        this.profileImage = user.getProfileImage();
        this.coverImage = user.getCoverImage();
        this.gender = user.getGender();
        this.stories = null;
        this.usertype = user.getUsertype();
        this.moods = user.getMoods();
        if(user.getUserInfo()!=null) {
            this.email = user.getUserInfo().getEmail();
            this.mobile = user.getUserInfo().getMobile();
        }
    }

}
