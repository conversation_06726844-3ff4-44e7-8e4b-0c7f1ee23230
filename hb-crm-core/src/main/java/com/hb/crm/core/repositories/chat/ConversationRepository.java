package com.hb.crm.core.repositories.chat;

import com.hb.crm.core.Enums.chat.ConversationTypes;
import com.hb.crm.core.beans.chat.OneChat.Conversation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ConversationRepository extends MongoRepository<Conversation, String> {
    
    // Find conversation by topic
    Optional<Conversation> findByTopic(String topic);

    // Check if user is participant in conversation
    @Query(value = "{ '_id': ?0, 'user.id': ?1 }", exists = true)
    boolean isUserParticipant(String conversationId, String userId);
    
    // New methods for finding conversations by userId
    @Query("{ 'user.id': ?0 }")
    List<Conversation> findByUserId(String userId);
    
    @Query("{ 'user.id': ?0, 'type': ?1 }")
    Page<Conversation> findByUserIdAndType(String userId, ConversationTypes type, Pageable pageable);

    @Query(value = "{ 'id': { $in: ?0 } }")
    List<Conversation> findConversationsById(List<String> conversationIds);

    @Aggregation(pipeline = {
            "{ $lookup: { from: 'chatMessage', localField: 'id', foreignField: 'conversation.id', as: 'messages' } }",
            "{ $match: { 'messages': { $ne: [] } } }",
            "{ $addFields: { 'lastMessage': { $max: '$messages.dateTime' } } }",
            "{ $match: { 'lastMessage': { $gte: ?0, $lte: ?1 } } }"
    })
    List<Conversation> findConversationsWithLastMessageInDateRange(LocalDateTime startDate, LocalDateTime endDate);

    Page<Conversation> findByIdIn(List<String> ids, Pageable pageable);
}
