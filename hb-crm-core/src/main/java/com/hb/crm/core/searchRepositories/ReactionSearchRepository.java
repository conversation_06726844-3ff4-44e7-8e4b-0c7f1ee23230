package com.hb.crm.core.searchRepositories;

 import com.hb.crm.core.Enums.EntityName;
 import com.hb.crm.core.Enums.EntityType;
 import com.hb.crm.core.searchBeans.ReactionSearch;
 import org.springframework.data.domain.Page;
 import org.springframework.data.mongodb.repository.MongoRepository;
 import org.springframework.data.domain.Pageable;

 import java.util.List;
 import java.util.Optional;

public interface ReactionSearchRepository extends MongoRepository<ReactionSearch, String> {
    List<ReactionSearch> findByEntityType(String entityType);

    Optional<ReactionSearch> findByEntityNameAndEntityTypeAndEntityIdAndUserId(
            EntityName entityName,
            EntityType entityType,
            String entityId,
            String userId
    );
    List<ReactionSearch> findByEntityNameInAndEntityTypeAndUserId(
            List<EntityName> entityNames,
            EntityType entityType,
            String userId
    );

    List<ReactionSearch> findByEntityNameAndEntityTypeAndUserId(
            EntityName entityName,
            EntityType entityType,
            String userId
    );

    List<ReactionSearch> findByEntityNameAndEntityTypeAndEntityId(
            EntityName entityName,
            EntityType entityType,
            String entityId
    );

    Page<ReactionSearch> findByEntityNameAndEntityTypeAndUserId(
            EntityName entityName,
            EntityType entityType,
            String userId,
            Pageable pageable
    );
}
