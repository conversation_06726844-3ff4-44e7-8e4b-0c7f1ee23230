package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.chat.GroupChat.GroupAdminConversation;
import com.hb.crm.core.beans.chat.GroupChat.GroupConversation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GroupAdminConversationRepository extends MongoRepository<GroupAdminConversation, String> {

    // For active admins only (endTime is null)
    Page<GroupAdminConversation> findByConversationAndEndTimeIsNull(
            GroupConversation conversation,
            Pageable pageable
    );

    // For all admins (both active and inactive)
    Page<GroupAdminConversation> findByConversation(
            GroupConversation conversation,
            Pageable pageable
    );

    Optional<GroupAdminConversation> findByConversationAndEmployeeAndEndTimeIsNull(
            GroupConversation conversation,
            Employee employee
    );

    List<GroupAdminConversation> findByConversationId(String conversationId);
}
