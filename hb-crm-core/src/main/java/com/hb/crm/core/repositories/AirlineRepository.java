package com.hb.crm.core.repositories;


import com.hb.crm.core.beans.FlightPackage.Airline;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AirlineRepository extends MongoRepository<Airline, String> {
    List<Airline> findByDepartureAirportAndArrivalAirport(String departureAirport, String arrivalAirport);


}


