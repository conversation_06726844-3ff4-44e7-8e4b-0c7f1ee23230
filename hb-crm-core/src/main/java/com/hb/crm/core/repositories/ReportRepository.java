package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Report;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportRepository extends MongoRepository<Report, String> {
    Page<Report> findByUserId(String userId, Pageable pageable);

    @Query("{ $and: [ " +
           "  { $or: [ " +
           "    { 'entityId': ?0 }, " +
           "    { 'entityId': { $exists: true }, $expr: { $eq: [?0, null] } }" +
           "  ] }, " +
           "  { $or: [ " +
           "    { 'entityName': ?1 }, " +
           "    { 'entityName': { $exists: true }, $expr: { $eq: [?1, null] } }" +
           "  ] }, " +
            "  { $or: [ " +
            "    { 'userId': ?2 }, " +
            "    { 'userId': { $exists: true }, $expr: { $eq: [?2, null] } }" +
            "  ] } " +
           "] }")
    Page<Report> search(String entityId, String entityName,String userId, Pageable pageable);
}
