package com.hb.crm.core.services;

import com.hb.crm.core.beans.Notification.CrmNotification;
import com.hb.crm.core.services.interfaces.EmailNotificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class EmailNotificationServiceImpl implements EmailNotificationService {

    private final JavaMailSender javaMailSender;

    @Value("${spring.mail.username:<EMAIL>}")
    private String FromEmail;


    public void send(CrmNotification notification) {

        SimpleMailMessage mailMessage = new SimpleMailMessage();
        mailMessage.setFrom(FromEmail);
        mailMessage.setReplyTo(FromEmail);
        mailMessage.setTo(notification.getUser().getUserInfo().getEmail());
        mailMessage.setSubject(notification.getTitle());
        mailMessage.setText(notification.getBody());
        javaMailSender.send(mailMessage);
    }

}
