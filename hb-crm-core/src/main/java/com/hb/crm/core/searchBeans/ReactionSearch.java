package com.hb.crm.core.searchBeans;
import com.hb.crm.core.Enums.EntityName;
import com.hb.crm.core.Enums.EntityType;
import com.hb.crm.core.Enums.ReactionType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import java.time.LocalDateTime;

@Setter
@Getter
public class ReactionSearch
{
    @Id
    private String id;
    @LastModifiedDate
    private LocalDateTime updated;
    private String userId;
    private String entityId;
    @Schema(example = "Post", description = "name of entity (e.g., Post, Package,FollowPackage, Comment, Media, User)")
    private EntityName entityName;
    @Schema(example = "React", description = "Type of entity reaction (e.g., React, PostMark, Favourite, Subscribe, Follow, View,Share)")
    private EntityType entityType;
    @Schema(example = "like", description = "Type of reaction (e.g., like, love, haha, wow, sad, angry)")
    private ReactionType reactionType;
}
