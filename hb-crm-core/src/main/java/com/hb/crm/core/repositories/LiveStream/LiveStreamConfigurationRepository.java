package com.hb.crm.core.repositories.LiveStream;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.hb.crm.core.beans.LiveStream.LiveStreamConfiguration;
import java.util.Optional;
import java.util.List;
public interface LiveStreamConfigurationRepository extends MongoRepository<LiveStreamConfiguration, String> 
{
    @Query("{'active': true}")
    Optional<LiveStreamConfiguration> findActiveConfiguration();
    
    @Query("{'active': true}")
    List<LiveStreamConfiguration> findAllActiveConfigurations();
    
    List<LiveStreamConfiguration> findAllByOrderByCreatedAtDesc();
}
