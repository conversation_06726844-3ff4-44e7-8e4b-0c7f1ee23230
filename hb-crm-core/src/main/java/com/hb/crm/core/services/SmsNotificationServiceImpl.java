package com.hb.crm.core.services;

import com.hb.crm.core.services.interfaces.SmsNotificationService;
import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import org.springframework.stereotype.Service;

@Service
public class SmsNotificationServiceImpl implements SmsNotificationService {

    private final String accountSid = "**********************************";

    private final String authToken = "f3b3e4353a971a3a36d8489eabb3ce00";

    private final String fromPhoneNumber = "+***********";


    public SmsNotificationServiceImpl() {
        Twilio.init(accountSid, authToken);
    }

    @Override
    public void send(String toPhoneNumber, String message) {

        Message.creator(
                        new PhoneNumber(toPhoneNumber),
                        new PhoneNumber(fromPhoneNumber),
                        message)
                .create();

    }


}
