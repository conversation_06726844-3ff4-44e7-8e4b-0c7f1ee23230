package com.hb.crm.core.exceptions;

public class ErrorDto {
	
	private String code;
	private String message;
	private String errorlogId;
	
	public ErrorDto() {
		
	}
	
	public ErrorDto(String code, String message, String errorlogId) {
		super();
		this.code = code;
		this.message = message;
		this.errorlogId = errorlogId;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getErrorlogId() {
		return errorlogId;
	}
	public void setErrorlogId(String errorlogId) {
		this.errorlogId = errorlogId;
	}
	

}
