package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.NotificationTemplateVariable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface NotificationTemplateVariableRepository extends MongoRepository<NotificationTemplateVariable, String> {
    Optional<NotificationTemplateVariable> findByNameAndEntity(String name, String entity);
}