package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.SubscribeStatus;
import com.hb.crm.core.beans.TravelerSubscribe;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface TravelerSubscribeRepository extends MongoRepository<TravelerSubscribe, String> {

    /**
     * Retrieves all traveler subscriptions for a specific user.
     * This method performs a simple MongoDB query to find all TravelerSubscribe documents
     * that match the given user ID.
     *
     * @param userId The unique identifier of the user
     * @return List of TravelerSubscribe objects associated with the specified user
     */
    @Query("{ 'user.$id': ?0 , 'subscribe.$id.user.$id': ?0}")
    List<TravelerSubscribe> findTravelerSubscribesByUserId(ObjectId userId);

//    @Aggregation(pipeline = {
//            "{ $match: { 'user.$id': ?0 } }",
//            "{ $lookup: { from: 'subscribe', localField: 'subscribe.id', foreignField: 'id', as: 'subscribeLookup' } }",
//    })
//    List<TravelerSubscribe> findTravelerSubscribesByUserId(ObjectId userId);



    /**
     * Retrieves a paginated list of traveler subscriptions for a specific user, excluding canceled packages.
     * This method performs a MongoDB aggregation pipeline that:
     *
     * <p>
     * 1. Matches documents by user ID <br>
     * 2. Performs a lookup to join with the subscribe collection <br>
     * 3. Unwinds the lookup results <br>
     * 4. Filters out canceled packages <br>
     * 5. Applies optional status filter if provided <br>
     * 6. Applies pagination using skip and limit
     *</p>
     *
     * @param userId The unique identifier of the user
     * @param status Optional subscription status filter (can be null)
     * @param skip   Number of documents to skip (pagination offset)
     * @param limit  Maximum number of documents to return
     * @return List of TravelerSubscribe objects matching the criteria
     */
    @Aggregation(pipeline = {
        "{ $match: { 'user.$id': ?0 } }",
        "{ $lookup: { from: 'subscribe', localField: 'subscribe.$id', foreignField: '_id', as: 'subscribeLookup' } }",
        "{ $unwind: '$subscribeLookup' }",
        "{ $match: { 'subscribeLookup.id._package.state': { $ne: 'Canceled' } } }",
        "{ $match: { $or: [ { 'subscribeLookup.status': ?1 }, { $expr: { $eq: [?1, null] } } ] } }",
        "{ $skip: ?2 }",
        "{ $limit: ?3 }"
    })
    List<TravelerSubscribe> findTravelerSubscribesByUserId(String userId, SubscribeStatus status, long skip, long limit);

    /**
     * Counts the total number of traveler subscriptions for a specific user, including handling null cases.
     * This method performs a MongoDB aggregation pipeline that:
     *
     * <p>
     * 1. Matches documents by user ID <br>
     * 2. Performs a lookup to join with the subscribe collection <br>
     * 3. Unwinds the lookup results while preserving null values <br>
     * 4. Matches non-canceled packages or null subscriptions <br>
     * 5. Applies optional status filter if provided <br>
     * 6. Counts the results, defaulting to 0 if no matches are found
     *</p>
     *
     * @param userId The unique identifier of the user
     * @param status Optional subscription status filter (can be null)
     * @return Total count of matching traveler subscriptions
     */
    @Aggregation(pipeline = {
        "{ $match: { 'user.$id': ?0 } }",
        "{ $lookup: { from: 'subscribe', localField: 'subscribe.$id', foreignField: '_id', as: 'subscribeLookup' } }",
        "{ $unwind: { path: '$subscribeLookup', preserveNullAndEmptyArrays: true } }",
        "{ $match: { $or: [ { 'subscribeLookup.id._package.state': { $ne: 'Canceled' } }, { 'subscribeLookup': null } ] } }",
        "{ $match: { $or: [ { 'subscribeLookup.status': ?1 }, { $expr: { $eq: [?1, null] } } ] } }",
        "{ $count: 'count' }",
        "{ $project: { count: { $ifNull: ['$count', 0] } } }"
    })
    Long countTravelerSubscribesByUserId(ObjectId userId, SubscribeStatus status);
}