package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.beans.Notification.CrmNotification;

import java.util.List;
import java.util.Map;

public interface PushNotificationService {


    public void send(List<CrmNotification> notifications);

    public void send(CrmNotification notification);

    /**
     *
     * @apiNote Sends notification to single user by fcm token.
     *
     * @param token
     * @param title
     * @param body
     */
    public void send(String token, String title, String body);

    /**
     *
     * @apiNote Send notification to single user by fcm token with data payload
     *
     * @param token
     * @param title
     * @param body
     * @param payload
     */
    public void send(
            String token,
            String title,
            String body,
            Map<String, String> payload
    );

    /**
     *
     * @apiNote Send broadcast notification to topic
     *
     * @param topic
     * @param title
     * @param body
     */
    public void sendBroadcast(String topic, String title, String body);


    /**
     *
     * @apiNote Send broadcast notification to topic with payload data
     *
     * @param topic
     * @param title
     * @param body
     * @param payload
     */
    public void sendBroadcast(
            String topic,
            String title,
            String body,
            Map<String, String> payload
    );

}
