package com.hb.crm.core.services;

import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.dtos.LiveStream.ViewerCountUpdateDto;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class LiveStreamViewerService {

    private final LiveStreamRepository liveStreamRepository;
    
    // In-memory storage for active viewers (you might want to use Redis for production)
    private final Map<String, Set<String>> activeViewers = new ConcurrentHashMap<>();

    public ViewerCountUpdateDto addViewer(String liveStreamId, String userId) {
        System.out.println("🔄 Adding viewer - Stream: " + liveStreamId + ", User: " + userId);
        
        // Add viewer to active viewers set
        activeViewers.computeIfAbsent(liveStreamId, k -> ConcurrentHashMap.newKeySet()).add(userId);
        
        System.out.println("👥 Active viewers for stream " + liveStreamId + ": " + activeViewers.get(liveStreamId));
        
        // Update database
        int viewerCount = updateViewerCountInDatabase(liveStreamId);
        
        ViewerCountUpdateDto result = new ViewerCountUpdateDto(liveStreamId, viewerCount, "JOIN");
        System.out.println("📈 Viewer count result: " + result);
        
        return result;
    }

    public ViewerCountUpdateDto removeViewer(String liveStreamId, String userId) {
        System.out.println("🔄 Removing viewer - Stream: " + liveStreamId + ", User: " + userId);
        
        // Remove viewer from active viewers set
        Set<String> viewers = activeViewers.get(liveStreamId);
        if (viewers != null) {
            viewers.remove(userId);
            if (viewers.isEmpty()) {
                activeViewers.remove(liveStreamId);
            }
        }
        
        System.out.println("👥 Active viewers for stream " + liveStreamId + ": " + activeViewers.get(liveStreamId));
        
        // Update database
        int viewerCount = updateViewerCountInDatabase(liveStreamId);
       
        ViewerCountUpdateDto result = new ViewerCountUpdateDto(liveStreamId, viewerCount, "LEAVE");
        System.out.println("📉 Viewer count result: " + result);
        
        return result;
    }

    private int updateViewerCountInDatabase(String liveStreamId) {
        System.out.println("💾 Updating database for stream: " + liveStreamId);
        
        try {
            LiveStream liveStream = liveStreamRepository.findById(liveStreamId)
                    .orElseThrow(() -> new IllegalArgumentException("Live stream not found"));
            
            System.out.println("✅ Found live stream: " + liveStream.getId() + " - " + liveStream.getTitle());
            
            Set<String> viewers = activeViewers.get(liveStreamId);
            int viewerCount = viewers != null ? viewers.size() : 0;
            
            System.out.println("📊 Calculated viewer count: " + viewerCount);
            System.out.println("📊 Previous viewer count: " + liveStream.getViewersCount());
            
            liveStream.setViewersCount(viewerCount);
            liveStreamRepository.save(liveStream);
            
            System.out.println("💾 Database updated successfully with count: " + viewerCount);
            
            return viewerCount;
        } catch (Exception e) {
            System.err.println("❌ Error updating database: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    public int getCurrentViewerCount(String liveStreamId) {
        Set<String> viewers = activeViewers.get(liveStreamId);
        return viewers != null ? viewers.size() : 0;
    }
}