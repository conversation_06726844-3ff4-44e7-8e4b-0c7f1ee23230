package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.Notification.NotificationMuteUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NotificationMuteUserRepository extends MongoRepository<NotificationMuteUser, String> {

    Optional<NotificationMuteUser> findByUserIdAndMutedUser_Id(String userId, String mutedUserId);

    Page<NotificationMuteUser> findByUserId(String userId, Pageable pageable);

    List<NotificationMuteUser> findByUserId(String userId);

    Page<NotificationMuteUser> findByUserIdAndMutedUser_Usertype(String userId, UserType type, Pageable pageable);

}
