package com.hb.crm.core.CombinedKeys;

import com.hb.crm.core.beans.Package;
import com.hb.crm.core.beans.User;
import org.springframework.data.mongodb.core.mapping.DBRef;

public class UserPackageKey {
    @DBRef
    private User user;
    @DBRef
    private Package _package;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Package get_package() {
        return _package;
    }

    public void set_package(Package _package) {
        this._package = _package;
    }

    public UserPackageKey() {
    }

    public UserPackageKey(User user, Package _package) {
        this.user = user;
        this._package = _package;
    }
}
