package com.hb.crm.core.repositories;


import com.hb.crm.core.beans.MediaReaction;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.Query;

public interface MediaReactionRepository extends MongoRepository<MediaReaction, String> {
    @Query("{ 'media.$id': ?0 }")
    Page<MediaReaction> findByMediaId(ObjectId mediaId, Pageable pageable);
}
