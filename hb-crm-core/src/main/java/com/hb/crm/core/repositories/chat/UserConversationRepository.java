package com.hb.crm.core.repositories.chat;

import com.hb.crm.core.beans.chat.GroupChat.UserConversation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

@Repository
public interface UserConversationRepository extends MongoRepository<UserConversation, String> {

    @Query("{ 'user.id': ?0, 'conversation.id': ?1 }")
    Optional<UserConversation> findByUserIdAndConversationId(String userId, String conversationId);

    @Query("{ 'conversation.id': ?0 }")
    Page<UserConversation> findByConversationId(String conversationId, Pageable pageable);

    @Query("{ 'conversation.id': ?0, 'endTime': null }")
    List<UserConversation> findByConversationIdAndEndTimeIsNull(String conversationId);

    @Query("{ 'conversation.id': ?0, 'endTime': null }")
    Page<UserConversation> findByConversationIdAndEndTimeIsNull(String conversationId, Pageable pageable);
}
