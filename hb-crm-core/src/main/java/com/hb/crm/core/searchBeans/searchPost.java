package com.hb.crm.core.searchBeans;

import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.beans.Tag;
import lombok.Data;

import java.util.List;

 @Data
public class searchPost extends BaseEntity {

    private String text;
    private List<MediaWrapperSearch> media;
    private float latitude;
    private float longtuid;
    private String place;
    private simpleUserInfo user;
    private List<simpleUserInfo> taggedUsers;
    private simplePackageInfo Package;
    private List<Tag> tags;
    private PackageStatus postStatus = PackageStatus.draft;
    private String rejectionNote;
    private String note;
    private int commentsCount;
    private int reactsCount;
    private int viewsCount;
    private int sharesCount;

     private String mapUrl;

     private String slug;
}
