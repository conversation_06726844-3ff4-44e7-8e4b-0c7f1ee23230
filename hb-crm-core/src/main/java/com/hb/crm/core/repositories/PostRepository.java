package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.PostType;
import com.hb.crm.core.beans.Post;
import com.hb.crm.core.beans.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface PostRepository extends MongoRepository<Post, String> {

    Optional<Post> findBySlug(String slug);
    boolean existsBySlug(String slug);
    Page<Post> findBookmarkedPostsByUserId(String userId, Pageable pageable);
    boolean existsBySlugIgnoreCase(String slug);

    /**
     * Find posts by list of IDs with pagination support
     */
    Page<Post> findByIdIn(List<String> ids, Pageable pageable);

    /**
     * Find posts by list of IDs with pagination support
     */
    Page<Post> findByIdInAndPostType(List<String> ids, PostType type, Pageable pageable);

    /**
     * Find posts by list of IDs without pagination
     */
    List<Post> findByIdIn(List<String> ids);

   

}
