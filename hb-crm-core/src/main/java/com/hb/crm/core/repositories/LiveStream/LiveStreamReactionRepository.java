package com.hb.crm.core.repositories.LiveStream;

import com.hb.crm.core.beans.LiveStream.LiveStreamReaction;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface LiveStreamReactionRepository extends MongoRepository<LiveStreamReaction, String> {
    
    @Query("{ 'liveStream.$id': ?0 }")
    Page<LiveStreamReaction> findByLiveStreamId(ObjectId liveStreamId, Pageable pageable);
    
    @Query("{ 'liveStream.$id': ?0 }")
    List<LiveStreamReaction> findAllByLiveStreamId(ObjectId liveStreamId);
    
    @Query("{ 'liveStream.$id': ?0, 'user.$id': ?1 }")
    Optional<LiveStreamReaction> findByLiveStreamIdAndUserId(ObjectId liveStreamId, ObjectId userId);
    
    @Query("{ 'liveStream.$id': ?0, 'user.$id': ?1 }")
    void deleteByLiveStreamIdAndUserId(ObjectId liveStreamId, ObjectId userId);
}