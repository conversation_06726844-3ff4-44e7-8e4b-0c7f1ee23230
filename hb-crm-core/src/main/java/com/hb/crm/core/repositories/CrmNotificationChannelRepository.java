package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.CrmNotificationChannelTypes;
import com.hb.crm.core.beans.Notification.CrmNotificationChannel;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Optional;

public interface CrmNotificationChannelRepository extends MongoRepository<CrmNotificationChannel, String> {
    Optional<CrmNotificationChannel> findOneByChannel(CrmNotificationChannelTypes channel);

}
