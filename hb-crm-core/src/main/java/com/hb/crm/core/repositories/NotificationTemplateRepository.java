package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.NotificationTemplate;
import com.hb.crm.core.Enums.NotificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NotificationTemplateRepository extends MongoRepository<NotificationTemplate, String> {

    List<NotificationTemplate> findByNotificationType(NotificationType notificationType);

    List<NotificationTemplate> findByStatusTrue();

    Optional<NotificationTemplate> findByNotificationTypeAndStatusTrue(NotificationType notificationType);

    @Query("{'$or': [{'subject': {'$regex': ?0, '$options': 'i'}}, {'body': {'$regex': ?0, '$options': 'i'}}]}")
    Page<NotificationTemplate> findBySubjectOrBodyContainingIgnoreCase(String searchTerm, Pageable pageable);

    long countByStatus(boolean status);

    /**
     * Count active templates by notification type
     */
    long countByNotificationTypeAndStatusTrue(NotificationType notificationType);
}