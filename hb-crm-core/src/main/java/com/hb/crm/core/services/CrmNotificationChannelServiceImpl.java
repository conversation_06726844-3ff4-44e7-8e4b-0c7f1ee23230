package com.hb.crm.core.services;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.hb.crm.core.beans.Notification.CrmNotificationChannel;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.notification.CreateCrmNotificationChannelDto;
import com.hb.crm.core.dtos.notification.UpdateCrmNotificationChannelDto;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.CrmNotificationChannelRepository;
import com.hb.crm.core.repositories.UserRepository;
import com.hb.crm.core.services.interfaces.CrmNotificationChannelService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class CrmNotificationChannelServiceImpl implements CrmNotificationChannelService {

    private final CrmNotificationChannelRepository repository;
    private final UserRepository userRepository;

    @Override
    public CrmNotificationChannel create(CreateCrmNotificationChannelDto dto) {

        var existingChannel = repository.findOneByChannel(dto.getChannelType());

        if(existingChannel.isPresent()) {
            throw new IllegalArgumentException("Channel " + dto.getChannelType() + " already exists");
        }

        var notificationChannel = new CrmNotificationChannel();
        notificationChannel.setName(dto.getName());
        notificationChannel.setChannel(dto.getChannelType());
        notificationChannel.setEnabled(dto.isEnabled());

        repository.save(notificationChannel);
        return notificationChannel;
    }

    @Override
    public CrmNotificationChannel update(UpdateCrmNotificationChannelDto dto) {

        var notificationChannel = repository.findById(dto.getId())
                .orElseThrow(() ->
                        new IllegalArgumentException(String.format("Channel with id  %s does not exist", dto.getId())));

        notificationChannel.setName(dto.getName());
        notificationChannel.setChannel(dto.getChannelType());
        notificationChannel.setEnabled(dto.isEnabled());
        repository.save(notificationChannel);

        return notificationChannel;
    }

    @Override
    public  String subscribeChannel(String id,String userId){
        var notificationChannel = repository.findById(id)
                .orElseThrow(() ->
                        new IllegalArgumentException(String.format("Channel with id  %s does not exist",id)));
         var user = userRepository.findById(userId)
                .orElseThrow(() ->
                        new IllegalArgumentException("issue  with current user "));

         return subscribeToTopic(user.getFcmTokens(),notificationChannel.getId());
    }


    public PageDto<CrmNotificationChannel>  search( int page, int limit){
        if (page < 0) {
            throw new CustomException(400, "Page number cannot be negative");
        }
        if (limit <= 0) {
            throw new CustomException(400, "Page size must be greater than 0");
        }
        Pageable pageable = PageRequest.of(page, limit);
        Page<CrmNotificationChannel> channels=  repository.findAll(pageable);

        PageDto<CrmNotificationChannel> pageDto = new PageDto<>();
        pageDto.setItems(channels.getContent());
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(limit);
        pageDto.setTotalNoOfItems(channels.getTotalElements());
        return pageDto;
    }
    public String subscribeToTopic(List<String> token, String topic) {
        try {
            FirebaseMessaging.getInstance().subscribeToTopic(token, topic);
            return "Successfully subscribed to topic: " + topic;
        } catch (FirebaseMessagingException e) {
            e.printStackTrace();
            return  "Fallen to  subscribe to topic: " + topic + "with error: " + e.getMessage();
        }
    }
    @Override
    public void delete(String id) {
        var notificationChannel = repository.findById(id).orElseThrow();
        repository.delete(notificationChannel);
    }
}
