package com.hb.crm.core.repositories.chat;

import com.hb.crm.core.Enums.ConversationMessageType;
import com.hb.crm.core.beans.chat.GroupChat.GroupChatMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.List;

@Repository
public interface GroupChatMessageRepository extends MongoRepository<GroupChatMessage, String> {
    @Query("{ 'conversation.id': ?0, 'deletedByMe': false }")
    Page<GroupChatMessage> findByConversationIdAndDateTimeBefore(
        String conversationId,
        Pageable pageable
    );
    

    @Query("{ 'conversation.id': ?0, 'deletedByMe': false, $or: [ { 'type': { $exists: false } }, { 'type': { $in: ?1 } } ] }")
    Page<GroupChatMessage> findByConversationIdAndTypesAndDateTimeBefore(
        String conversationId,
        List<ConversationMessageType> types,
        Pageable pageable
    );

    // Get message by conversation ID and poll ID
    @Query("{ 'conversation.id': ?0, 'type': 'Poll', 'poll.id': ?1 }")
    Optional<GroupChatMessage> findByConversationIdAndPollId(String conversationId, String pollId);

    @Query("{ 'conversation.id': ?0, 'deletedByMe': false," +
            " 'userGroupChatMessages': { $elemMatch: { 'user.id': ?1, 'status': { $ne: 'Read' } } } }")
    long countUnreadMessagesByConversationAndUserId(String conversationId, String userId);

    @Query("{ 'deletedByMe': false, 'userGroupChatMessages': { $elemMatch: { 'user.id': ?0, 'status': { $ne: 'Read' } } } }")
    long countAllUnreadMessagesByUserId(String userId);
}
