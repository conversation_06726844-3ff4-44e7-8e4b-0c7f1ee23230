package com.hb.crm.core.services;

import com.hb.crm.core.Enums.CrmNotificationChannelTypes;
import com.hb.crm.core.beans.Notification.CrmNotificationChannel;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.beans.Notification.CrmNotification;
import com.hb.crm.core.dtos.notification.CreateCrmNotificationDto;
import com.hb.crm.core.dtos.notification.CrmNotificationPayload;
import com.hb.crm.core.repositories.CrmNotificationChannelRepository;
import com.hb.crm.core.repositories.CrmNotificationRepository;
import com.hb.crm.core.repositories.UserRepository;
import com.hb.crm.core.services.interfaces.CrmNotificationService;
import com.hb.crm.core.services.interfaces.EmailNotificationService;
import com.hb.crm.core.services.interfaces.PushNotificationService;
import com.hb.crm.core.services.interfaces.SmsNotificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;


/**
 * Implementation of {@link CrmNotificationService} that provides notification management
 * functionality including sending, retrieving, and filtering notifications.
 * <p>
 * This service handles notifications across multiple channels (Push, Email, SMS)
 * and provides administrative and user-specific notification operations.
 * </p>
 *
 * @see CrmNotificationService
 * @see CrmNotification
 * @see CrmNotificationChannelTypes
 */
@Service
@RequiredArgsConstructor
public class CrmNotificationServiceImpl implements CrmNotificationService {

    private final CrmNotificationRepository notificationRepository;
    private final MongoTemplate mongoTemplate;
    private final CrmNotificationChannelRepository notificationChannelRepository;
    private final UserRepository userRepository;

    private final PushNotificationService pushNotificationService;
    private final SmsNotificationService smsNotificationService;
    private final EmailNotificationService emailNotificationService;
    private final CrmNotificationRepository crmNotificationRepository;
    private final CrmNotificationChannelRepository crmNotificationChannelRepository;


    @Override
    public CrmNotification createNotification(CreateCrmNotificationDto notification) {

        var user = userRepository.findById(notification.getUserId())
                .orElseThrow(() -> new IllegalArgumentException(String.format("User with id %s not found", notification.getUserId())));
        var channel = crmNotificationChannelRepository.findOneByChannel(notification.getChannel())
                .orElseThrow(() -> new IllegalArgumentException(String.format("Channel with type %s not found", notification.getChannel())));

        var newNotification = new CrmNotification();
        newNotification.setTopic(notification.getTopic());
        newNotification.setBody(notification.getBody());
        newNotification.setChannel(channel);
        newNotification.setMobileNumber(newNotification.getMobileNumber());
        newNotification.setUser(user);
        newNotification.setRead(notification.isRead());
        newNotification.setSentAt(notification.getSentAt());
        newNotification.setReadDate(notification.getReadDate());

        HashMap<String, String> payload = new HashMap<>();
        for (CrmNotificationPayload payloadItems: notification.getPayload()) {
            payload.put(payloadItems.getKey(), payloadItems.getValue());
        }
        newNotification.setPayload(payload);
        crmNotificationRepository.save(newNotification);

        return newNotification;
    }

    @Override
    public CrmNotification sendNotification(CrmNotification notification) {
        var channel = notification.getChannel();

        switch (channel.getChannel()) {
            case CrmNotificationChannelTypes.Push -> pushNotificationService.send(notification);
            case CrmNotificationChannelTypes.Email -> emailNotificationService.send(notification);
            case CrmNotificationChannelTypes.Sms -> smsNotificationService.send(notification.getMobileNumber(), notification.getBody());
            default -> throw new IllegalStateException("Unexpected value: " + channel.getChannel());
        }

        notification.setSentAt(LocalDateTime.now());
        notificationRepository.save(notification);
        return notification;
    }

    @Override
    public CrmNotification getNotificationItem(String id) {
        return notificationRepository.findById(id).orElseThrow();
    }

    @Override
    public CrmNotification setReadStatus(boolean read, String id) {
        var notification = this.notificationRepository.findById(id).orElseThrow();
        notification.setRead(read);
        if (read) {
            notification.setReadDate(LocalDateTime.now());
        }
        return this.notificationRepository.save(notification);
    }

    @Override
    public List<CrmNotification> filterAdminNotifications(String searchText,
                                                          LocalDateTime fromDate,
                                                          LocalDateTime toDate,
                                                          Boolean read,
                                                          CrmNotificationChannelTypes type,
                                                          int pageNumber,
                                                          int pageSize) {

        Criteria criteria = new Criteria();

        return filterNotifications(
                criteria,
                searchText,
                fromDate,
                toDate,
                read,
                null,
                type,
                pageNumber,
                pageSize);
    }

    @Override
    public List<CrmNotification> filterUserNotification(String searchText,
                                                        LocalDateTime fromDate,
                                                        LocalDateTime toDate,
                                                        Boolean read,
                                                        String username,
                                                        CrmNotificationChannelTypes type,
                                                        int pageNumber,
                                                        int pageSize) {
        Criteria criteria = new Criteria();

        var user = userRepository.findByUsername(username).orElseThrow();

        return filterNotifications(
                criteria,
                searchText,
                fromDate,
                toDate,
                read,
                user,
                type,
                pageNumber,
                pageSize);
    }


    @Override
    public void delete(String id) {
        crmNotificationRepository.deleteById(id);
    }

    @Override
    public List<CrmNotification> seed() {

        CrmNotificationChannel pushChannel = new CrmNotificationChannel();
        pushChannel.setName("Push");
        pushChannel.setEnabled(true);;
        pushChannel.setChannel(CrmNotificationChannelTypes.Push);
        notificationChannelRepository.save(pushChannel);

        CrmNotificationChannel smsChannel = new CrmNotificationChannel();
        smsChannel.setName("Sms");
        smsChannel.setEnabled(true);;
        smsChannel.setChannel(CrmNotificationChannelTypes.Sms);
        notificationChannelRepository.save(smsChannel);

        CrmNotificationChannel emailChannel = new CrmNotificationChannel();
        emailChannel.setName("Email");
        emailChannel.setEnabled(true);;
        emailChannel.setChannel(CrmNotificationChannelTypes.Email);
        notificationChannelRepository.save(emailChannel);

        CrmNotificationChannel inAppChannel = new CrmNotificationChannel();
        inAppChannel.setName("In App");
        inAppChannel.setEnabled(true);;
        inAppChannel.setChannel(CrmNotificationChannelTypes.InApp);
        notificationChannelRepository.save(inAppChannel);


        final Pageable pageable = PageRequest.of(0, 10);
        final  Criteria searchQuery = new Criteria();
        Aggregation agg = Aggregation.newAggregation(
                Aggregation.skip((long) pageable.getPageNumber() * pageable.getPageSize()),
                Aggregation.limit(pageable.getPageSize())
        );

        List<User> aggregationResults = mongoTemplate.aggregate(agg, "user", User.class).getMappedResults();


        CrmNotificationChannel[] channels = {
                pushChannel,
                smsChannel,
                emailChannel,
                inAppChannel
        };

        Random random = new Random();

        List<CrmNotification> notifications = new ArrayList<>();

        for (int i = 0; i < 100; i++) {

            CrmNotification notification = new CrmNotification();
            notification.setTitle(String.format("Title_%s", random.nextInt(100)));
            notification.setBody(String.format("Body_%s", random.nextInt(100)));
            notification.setRead(random.nextBoolean());
            notification.setChannel(channels[random.nextInt(channels.length)]);
            notification.setSentAt(LocalDateTime.now());
            notification.setReadDate(LocalDateTime.now());
            notification.setTopic(String.format("topic_%s",random.nextInt(99)));
            notification.setMobileNumber(String.format("+9639%s", random.nextInt(99999) + 10000));
            notification.setUser(aggregationResults.get(random.nextInt(aggregationResults.size())));
            notificationRepository.save(notification);
            notifications.add(notification);
        }

        return notifications;
    }

    private List<CrmNotification> filterNotifications(
            Criteria criteria,
            String searchText,
            LocalDateTime fromDate,
            LocalDateTime toDate,
            Boolean read,
            User user,
            CrmNotificationChannelTypes type,
            int pageNumber,
            int pageSize) {

        Pageable pageable = PageRequest.of(
                pageNumber,
                pageSize
        );

        if (StringUtils.hasText(searchText)) {
            criteria.orOperator(
                    Criteria.where("title").regex(searchText, "i"),
                    Criteria.where("body").regex(searchText, "i")
            );
        }

        if (fromDate != null && toDate != null) {
            criteria.and("sentAt").gte(fromDate).lte(toDate);
        } else if (fromDate != null) {
            criteria.and("sentAt").gte(fromDate);
        } else if (toDate != null) {
            criteria.and("sentAt").lte(toDate);
        }

        if (user != null) {
            criteria.and("user").is(user);
        }

        if (type != null) {
            var channel = notificationChannelRepository.findOneByChannel(type)
                    .orElseThrow(() -> new IllegalArgumentException(String.format("User with username %s not found", type)));;
            criteria.and("channel").is(channel);
        }

        if (read != null) {
            criteria.and("read").is(read);
        }

        Aggregation agg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.sort(Sort.Direction.DESC, "sentAt"),
                Aggregation.skip((long) pageable.getPageNumber() * pageable.getPageSize()),
                Aggregation.limit(pageable.getPageSize())
        );

        // Execute aggregation
        AggregationResults<CrmNotification> results = mongoTemplate.aggregate(
                agg,
                CrmNotification.class,
                CrmNotification.class
        );
        return results.getMappedResults();
    }


}
