package com.hb.crm.core.searchRepositories;

import com.hb.crm.core.dtos.searchPostResultDto;
import com.hb.crm.core.searchBeans.searchPost;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface SearchPostRepository extends MongoRepository<searchPost, String> {

    /**
     * Performs a full-text search across multiple fields using MongoDB's $search aggregation.
     * Searches through text, usernames, package names and descriptions.<br><br>
     * The search pipeline consists of three stages:<br><br>
     * 1. $search - Performs the actual text search with compound conditions
     * 2. $facet - Handles pagination and total count calculation
     * 3. $project - Final projection with null handling for count
     *
     * @param search The search keyword to match against multiple fields
     * @param skip Number of documents to skip (for pagination)
     * @param limit Maximum number of documents to return
     * @return searchPostResultDto containing filtered results and total count
     */
     @Aggregation(pipeline = {
            // Search stage - Uses MongoDB Atlas Search with compound operator
            "{" +
            "    $search: { " +
            "        'index': 'default', " +
            "        'compound': { " +
            "            'must': [" +  // Ensures we only search within searchPost documents
            "                { 'equals': { 'path': '_class', 'value': 'com.hb.crm.core.searchBeans.searchPost' } }" +
            "            ]," +
            "            'should': [" +  // Wildcard searches across multiple fields
            "                { 'wildcard': { 'path': 'text', 'query': '*?0*', 'allowAnalyzedField': true } }," +
            "                { 'wildcard': { 'path': 'user.firstName', 'query': '*?0*', 'allowAnalyzedField': true } }," +
            "                { 'wildcard': { 'path': 'user.lastName', 'query': '*?0*', 'allowAnalyzedField': true } }," +
            "                { 'wildcard': { 'path': 'Package.name', 'query': '*?0*', 'allowAnalyzedField': true } }," +
            "                { 'wildcard': { 'path': 'Package.description', 'query': '*?0*', 'allowAnalyzedField': true } }" +
            "            ]," +
            "            'minimumShouldMatch': 1" + // At least one should condition must match
            "        } " +
            "    } " +
            "}",
            // Pagination facet - Handles skip/limit and total count in parallel
            "{" +
            "    $facet: {" +
            "        filteredResults: [ { $skip: ?1 }, { $limit: ?2 } ]," +
            "        totalCount: [ { $count: 'total' } ]" +
            "    }" +
            "}",
            // Final projection - Handles null count case
            "{" +
            "    $project: {" +
            "        filteredResults: 1," +
            "        totalCount: {" +
            "            $cond: {" +
            "                if: { $eq: [ { $size: '$totalCount' }, 0 ] }," +
            "                then: 0," +
            "                else: { $arrayElemAt: [ '$totalCount.total', 0 ] }" +
            "            }" +
            "        }" +
            "    }" +
            "}"
    })
    searchPostResultDto findByKeywordWithPagination(String search, long skip, int limit);

    /**
     * Retrieves all posts with pagination support.
     * Used when no search keyword is provided.
     *
     * @param pageable Pagination information
     * @return Page of searchPost entities
     */
    @Query("{ '_class': 'com.hb.crm.core.searchBeans.searchPost' }")
    Page<searchPost> findAllPosts(Pageable pageable);
    /**
     * Convenience method that combines search and pagination functionality.
     * If no search term is provided, returns all posts with pagination.
     *
     * @param search The search keyword (can be null or empty)
     * @param pageable Pagination information
     * @return Page of searchPost entities matching the search criteria
     */
    default Page<searchPost> findByKeyword(String search, Pageable pageable) {
        // If no search term, return all posts
        if (search == null || search.trim().isEmpty()) {
            return findAllPosts(pageable);
        }
        // Perform search with pagination
        searchPostResultDto result = findByKeywordWithPagination(
                search,
                (long) pageable.getPageNumber() * pageable.getPageSize(),
                pageable.getPageSize()
        );
        // Convert result to Page object
        return new PageImpl<>(result.getFilteredResults(), pageable, result.getTotalCount());
    }
}