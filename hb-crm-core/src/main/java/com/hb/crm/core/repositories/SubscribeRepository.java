package com.hb.crm.core.repositories;

import com.hb.crm.core.CombinedKeys.SubscribeKey;
import com.hb.crm.core.Enums.SubscribeStatus;
import com.hb.crm.core.beans.Subscribe;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface SubscribeRepository extends MongoRepository<Subscribe, SubscribeKey> {
    /**
     * Retrieves a paginated list of active subscriptions for a specific user.
     *
     * @param userId   The unique identifier of the user
     * @param status   Optional status filter (can be null)
     * @param pageable Pagination information
     * @return A Page containing Subscribe objects for the specified user, excluding canceled packages
     */
    @Query("{ '_id.user.$id': ?0, '_id._package.state': { $ne: 'Canceled'}" +
            ",$or: [{ 'status': { $exists: false } }, { 'status': ?1 }, { $expr: { $eq: [?1, null] }}]}"
    )
    Page<Subscribe> findSubscribedPackagesByUserId(ObjectId userId, SubscribeStatus status, Pageable pageable);

    @Query("{ '_id._package.$id': ?0, '_id._package.state': { $ne: 'Canceled'}" +
            ",$or: [{ 'status': { $exists: false } }, { 'status': ?1 }, { $expr: { $eq: [?1, null] }}]}")
    List<Subscribe> findSubscribedUserByPackageId(ObjectId packageId, SubscribeStatus status);


    Optional<Subscribe> findById(String subscribeId);


    List<Subscribe> findById_User_IdAndId__package_Id(String userId, String packageId);

}
