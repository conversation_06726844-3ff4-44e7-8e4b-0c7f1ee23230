package com.hb.crm.core.searchRepositories;

import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.Media;
import com.hb.crm.core.dtos.MediaWithUserDto;
import com.hb.crm.core.searchBeans.searchPackage;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface SearchPackageRepository extends MongoRepository<searchPackage, String> {

    @Query("{ 'packageType': ?0,  'packageStatus': 'posted' }")
    Page<searchPackage> findAllPackagesByType(PackageType type, Pageable pageable);
    Optional<searchPackage> findByPackageIdAndPackageType(String packageId, PackageType packageType);

    /**
     * Counts the number of search packages associated with a specific influencer.
     *
     * @param influencerId The ObjectId of the influencer
     * @return The count of search packages for the given influencer
     */
    @Query(value = "{ 'infulancer._id': ?0 }", count = true)
    long countByInfulancer_Id(ObjectId influencerId);

    /**
     * Searches for media items of type 'reel' within search packages by matching the given query
     * against the package's name and description.
     * <p>
     * This method utilizes MongoDB's aggregation framework with full-text search capabilities.
     * The process follows these steps:
     * <ul>
     *     <li>Performs a full-text search using the `$search` stage, applying autocomplete matching on the `name` and `description` fields of search packages.</li>
     *     <li>Unwinds the `medias` array to process each media item individually.</li>
     *     <li>Replaces the document root with the `medias.media` object, extracting media items from the package.</li>
     *     <li>Filters the results to include only media items with `mediaType` set to 'reel'.</li>
     * </ul>
     *
     * @param query The search query to match against the name and description of search packages
     * @return A list of media items of type 'reel' that match the search criteria
     */
    @Aggregation(pipeline = {
        // Search Stage: Finds matching search packages by name and description
        "{ " +
            "$search: { " +
                "'index': 'autocomplete', " +
                "'compound': { " +
                    "'should': [ " +
                        "{ 'autocomplete': { 'query': ?0, 'path': 'name' } }, " +
                        "{ 'autocomplete': { 'query': ?0, 'path': 'description' } } " +
                    "], " +
                    "'minimumShouldMatch': 1 " +
                "} " +
            "} " +
        "} ",
        // Unwind the 'medias' array to process individual media items
        "{ $unwind: '$medias' }",
        // Replace root with the inner 'medias.media' object
        "{ $replaceRoot: { 'newRoot': '$medias.media' } }",
        // Filter results to include only reels
        "{ $match: { mediaType: 'reel' } }",

        // Convert userId to ObjectId for proper joining
        "{ $addFields: { userId: { $toObjectId: '$userId' } } }",

        // Join with user collection
        "{ $lookup: { " +
                "    from: 'user', " +
                "    localField: 'userId', " +
                "    foreignField: '_id', " +
                "    as: 'user' " +
                "} }",

        // Unwrap user array to single object
        "{ $unwind: { path: '$user' } }",

        // Project fields for MediaWithUserDto
        "{ $project: { " +
                "    'id': '$_id', " +
                "    'title': '$title', " +
                "    'creationDate': '$creationDate', " +
                "    'lastUpdate': '$lastUpdate', " +
                "    'source': '$source', " +
                "    'description': '$description', " +
                "    'videoUrl': '$videoUrl', " +
                "    'imageCategory': '$imageCategory', " +
                "    'videoDuration': '$videoDuration', " +
                "    'videoDurationMS': '$videoDurationMS', " +
                "    'thumbnailClipUrl': '$thumbnailClipUrl', " +
                "    'thumbnailCaptureUrl': '$thumbnailCaptureUrl', " +
                "    'mediaType': '$mediaType', " +
                "    'OwnerId': '$OwnerId', " +
                "    'videoSize': '$videoSize', " +
                "    'LastUpdaterId': '$LastUpdaterId', " +
                "    'employee': '$employee', " +
                "    'numberOfReactions': '$numberOfReactions', " +
                "    'numberOfComments': '$numberOfComments', " +
                "    'userId': '$userId', " +
                "    'user': { " +
                "        'id': { '$toString': '$user._id' }, " +
                "        'profileImage': '$user.profileImage', " +
                "        'firstName': '$user.firstName', " +
                "        'lastName': '$user.lastName', " +
                "        'about': '$user.about', " +
                "        'username': '$user.username' " +
                "    } " +
                "} }"
    })
    List<MediaWithUserDto> searchReels(String query);

    /**
     * Searches for posted packages based on search term, location IDs, and optional package type with pagination.
     *
     * @param searchTerm The term to search for in package details and influencer information
     * @param countryIds List of country property IDs to filter by
     * @param cityIds List of city property IDs to filter by
     * @param type Optional package type filter
     * @param pageable Pagination information
     * @return A page of posted search packages matching the criteria
     */
    @Query("{ '$and': [ " +
            "{ 'packageStatus': 'posted' }, " +  // Always filter for posted packages
            "{ '$or': [ " +
                "{'name': {$regex: ?0, $options: 'i'}}, " +
                "{'description': {$regex: ?0, $options: 'i'}}, " +
                "{'infulancer.firstName': {$regex: ?0, $options: 'i'}}, " +
                "{'infulancer.lastName': {$regex: ?0, $options: 'i'}}, " +
                "{'packagePlaces': { " +
                    "$elemMatch: { " +
                        "$or: [ " +
                            "{'propertyId': { $in: ?1 }}, " +
                            "{'cities.propertyId': { $in: ?2 }} " +
                        "] " +
                    "} " +
                "}} " +
            "]}, " +
            "{ $expr: { $or: [ " +
                "{ $eq: [?3, null] }, " +
                "{ $eq: ['$packageType', ?3] } " +
            "]}} " +
        "]}")
    Page<searchPackage> findBySearchTermAndLocationIds(String searchTerm,
                                                      List<String> countryIds,
                                                      List<String> cityIds,
                                                      PackageType type,
                                                      Pageable pageable);

    /**
     * Retrieves all packages with pagination.
     *
     * @param pageable Pagination information
     * @return A page of all search packages
     */
    @Query("{ 'packageStatus': 'posted' }")
    Page<searchPackage> findAllPackages(Pageable pageable);


    /**
     * Explores packages based on optional search criteria and location filters.
     * If search term is empty, returns all packages.
     *
     * @param searchTerm Optional search term for filtering packages
     * @param type Optional filtering on package type
     * @param countryIds List of country property IDs to filter by
     * @param cityIds List of city property IDs to filter by
     * @param pageable Pagination information
     * @return A page of search packages matching the criteria or all packages if no search term is provided
     */
    default Page<searchPackage> explorePackages(String searchTerm,
                                                PackageType type,
                                                List<String> countryIds,
                                                List<String> cityIds,
                                                Pageable pageable) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            if(type == null)
                return findAllPackages(pageable);
            else
                return findAllPackagesByType(type, pageable);
        }

        return findBySearchTermAndLocationIds(searchTerm, countryIds, cityIds, type, pageable);
    }

    /**
     * Searches for packages by query within a specific list of package IDs using Atlas Search.
     * This method is particularly useful for searching within a user's subscribed packages.
     *
     * @param query The search query to match against package name, description, and location
     * @param packageIds List of package IDs to restrict the search to
     * @param limit Pagination information
     * @param skip Pagination information
     * @return A page of search packages matching the criteria and within the provided IDs
     */
    @Aggregation(pipeline = {
        // Search Stage: Uses Atlas Search with compound operator
        "{ " +
            "$search: { " +
                "'index': 'default', " +
                "'compound': { " +
                    "'must': [" +
                        "{ 'equals': { 'path': '_class', 'value': 'com.hb.crm.core.searchBeans.searchPackage' } }" +
                    "]," +
                    "'should': [" +
                        "{ 'wildcard': { 'path': 'name', 'query': '*?0*', 'allowAnalyzedField': true, 'score': { 'boost': { 'value': 3 } } } }, " +
                        "{ 'wildcard': { 'path': 'description', 'query': '*?0*', 'allowAnalyzedField': true, 'score': { 'boost': { 'value': 2 } } } }, " +
                        "{ 'wildcard': { 'path': 'infulancer.firstName', 'query': '*?0*', 'allowAnalyzedField': true } }, " +
                        "{ 'wildcard': { 'path': 'infulancer.lastName', 'query': '*?0*', 'allowAnalyzedField': true } } " +
                    "]," +
                    "'minimumShouldMatch': 1 " +
                "} " +
            "} " +
        "} ",
        // Filter by package IDs
        "{ $match: { '_id': { $in: ?1 } } }",
        // Skip and limit for pagination
        "{ $skip: ?2 }",
        "{ $limit: ?3 }"
    })
    List<searchPackage> searchInPackageIds(String query, List<String> packageIds, long skip, long limit);

    /**
     * Counts the total number of packages matching a search query within a specific list of package IDs.
     *
     * @param query The search query to match against package name, description, and location
     * @param packageIds List of package IDs to restrict the search to
     * @return The count of matching packages
     */
    @Aggregation(pipeline = {
        // Search Stage: Uses Atlas Search with compound operator
        "{ " +
            "$search: { " +
                "'index': 'default', " +
                "'compound': { " +
                    "'must': [" +
                        "{ 'equals': { 'path': '_class', 'value': 'com.hb.crm.core.searchBeans.searchPackage' } }" +
                    "]," +
                    "'should': [" +
                        "{ 'wildcard': { 'path': 'name', 'query': '*?0*', 'allowAnalyzedField': true, 'score': { 'boost': { 'value': 3 } } } }, " +
                        "{ 'wildcard': { 'path': 'description', 'query': '*?0*', 'allowAnalyzedField': true, 'score': { 'boost': { 'value': 2 } } } }, " +
                        "{ 'wildcard': { 'path': 'infulancer.firstName', 'query': '*?0*', 'allowAnalyzedField': true } }, " +
                        "{ 'wildcard': { 'path': 'infulancer.lastName', 'query': '*?0*', 'allowAnalyzedField': true } } " +
                    "]," +
                    "'minimumShouldMatch': 1 " +
                "} " +
            "} " +
        "} ",
        // Filter by package IDs
        "{ $match: { '_id': { $in: ?1 } } }",
        // Count the results
        "{ $count: 'count' }"
    })
    Long countSearchInPackageIds(String query, List<String> packageIds);


    @Aggregation(pipeline = {
            // 1. Search Stage: Searches for matching documents based on the provided query
            "{ " +
                    "$search: { " +
                    "'index': 'default', " +
                    "'compound': { " +
                    "'should': [ " +
                    "{ 'autocomplete': { 'query': ?0, 'path': 'name' } }, " +
                    "{ 'autocomplete': { 'query': ?0, 'path': 'description' } } " +
                    "], " +
                    "'minimumShouldMatch': 1 " +
                    "} " +
                    "} " +
                    "} ",

            // Unwind the 'medias' array
            "{ $unwind: '$medias' }",

            // Replace root with the inner 'medias.media' object
            "{ $replaceRoot: { 'newRoot': '$medias.media' } }",

            // Filters media to fetch only reels
            "{ $match: { mediaType: 'reel' } }",

            // Sampling for random selection
            "{ $addFields: { randomOrder: { $rand: {} } } }",
            "{ $sort: { randomOrder: 1 } }" // Random sorting
    })
    List<Media> findRandomReelsWithSearch(String query);
}