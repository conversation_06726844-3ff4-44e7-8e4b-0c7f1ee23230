package com.hb.crm.core.repositories.chat;

import com.hb.crm.core.beans.chat.GroupChat.GroupConversation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GroupConversationRepository extends MongoRepository<GroupConversation, String> {

    @Query("{ 'Package.$id': ObjectId('?0') }")
    List<GroupConversation> findByPackageId(String packageId);

    Page<GroupConversation> findByIdIn(List<String> ids, Pageable pageable);
}
