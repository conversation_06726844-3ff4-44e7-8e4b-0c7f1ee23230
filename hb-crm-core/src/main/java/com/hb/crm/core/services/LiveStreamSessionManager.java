package com.hb.crm.core.services;

import lombok.RequiredArgsConstructor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import com.hb.crm.core.dtos.LiveStream.StreamEndNotificationDto;
import com.hb.crm.core.CashService.CashService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class LiveStreamSessionManager 
{
    private final SimpMessagingTemplate messagingTemplate;
    private final CashService cashService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // Cache key patterns
    private static final String STREAM_SESSIONS_KEY = "live_stream:sessions:";
    private static final String SESSION_DETAILS_KEY = "live_stream:session_detail:";
    private static final String STREAM_OWNER_KEY = "live_stream:owner:";
    private static final String STREAMER_NAME_KEY = "live_stream:streamer_name:";
    private static final String ACTIVE_STREAMS_KEY = "live_stream:active_streams";
    
    // Session expiration: 24 hours
    private static final Long SESSION_EXPIRATION = TimeUnit.HOURS.toMillis(24);
    
    public static class SessionInfo 
    {
        private String sessionId;
        private String userId;
        private String username;
        private String streamId;
        private boolean isStreamer;
        
        // Default constructor for JSON deserialization
        public SessionInfo() {}
        
        public SessionInfo(String sessionId, String userId, String username, String streamId, boolean isStreamer) 
        {
            this.sessionId = sessionId;
            this.userId = userId;
            this.username = username;
            this.streamId = streamId;
            this.isStreamer = isStreamer;
        }
        
        // Getters and setters for JSON serialization
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getStreamId() { return streamId; }
        public void setStreamId(String streamId) { this.streamId = streamId; }
        
        public boolean isStreamer() { return isStreamer; }
        public void setStreamer(boolean streamer) { isStreamer = streamer; }
    }
    
    /**
     * Add a session to a stream
     */
    public void addSession(String streamId, String sessionId, String userId, String username, boolean isStreamer) 
    {
        try 
        {
            // Add to stream sessions
            Set<String> sessions = getStreamSessionsFromCache(streamId);
            sessions.add(sessionId);
            cashService.storeData(STREAM_SESSIONS_KEY + streamId, sessions, SESSION_EXPIRATION);
            
            // Store session details
            SessionInfo sessionInfo = new SessionInfo(sessionId, userId, username, streamId, isStreamer);
            cashService.storeData(SESSION_DETAILS_KEY + sessionId, sessionInfo, SESSION_EXPIRATION);
            
            // Track streamer
            if (isStreamer) 
            {
                cashService.storeData(STREAM_OWNER_KEY + streamId, userId, SESSION_EXPIRATION);
                cashService.storeData(STREAMER_NAME_KEY + streamId, username, SESSION_EXPIRATION);
            }
            
            // Add to active streams list
            Set<String> activeStreams = getActiveStreamsFromCache();
            activeStreams.add(streamId);
            cashService.storeData(ACTIVE_STREAMS_KEY, activeStreams, SESSION_EXPIRATION);
            
        } 
        catch (Exception e) 
        {
            System.err.println("Error adding session to cache: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Remove a session from a stream
     */
    public void removeSession(String sessionId) 
    {
        try {
            SessionInfo sessionInfo = getSessionInfoFromCache(sessionId);
            
            if (sessionInfo != null) 
            {
                String streamId = sessionInfo.getStreamId();
                
                // Remove from stream sessions
                Set<String> sessions = getStreamSessionsFromCache(streamId);
                sessions.remove(sessionId);
                
                if (sessions.isEmpty()) 
                {
                    cashService.deleteData(STREAM_SESSIONS_KEY + streamId);
                    // Remove from active streams
                    Set<String> activeStreams = getActiveStreamsFromCache();
                    activeStreams.remove(streamId);
                    cashService.storeData(ACTIVE_STREAMS_KEY, activeStreams, SESSION_EXPIRATION);
                } else {
                    cashService.storeData(STREAM_SESSIONS_KEY + streamId, sessions, SESSION_EXPIRATION);
                }
                
                // Remove session details
                cashService.deleteData(SESSION_DETAILS_KEY + sessionId);
                
                // Remove streamer ownership if it was the streamer
                if (sessionInfo.isStreamer()) 
                {
                    cashService.deleteData(STREAM_OWNER_KEY + streamId);
                    cashService.deleteData(STREAMER_NAME_KEY + streamId);
                }
            }
        } catch (Exception e) {
            System.err.println("Error removing session from cache: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Get all sessions for a stream
     */
    public Set<String> getStreamSessions(String streamId) 
    {
        return getStreamSessionsFromCache(streamId);
    }
    
    /**
     * Get session info
     */
    public SessionInfo getSessionInfo(String sessionId) 
    {
        return getSessionInfoFromCache(sessionId);
    }
    
    /**
     * Check if user is the streamer for a stream
     */
    public boolean isStreamer(String streamId, String userId) 
    {
        try {
            Object streamerIdObj = cashService.retrieveData(STREAM_OWNER_KEY + streamId);
            String streamerId = streamerIdObj != null ? streamerIdObj.toString() : null;
            return streamerId != null && streamerId.equals(userId);
        } catch (Exception e) {
            System.err.println("Error checking streamer status: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get streamer ID for a stream
     */
    public String getStreamerId(String streamId) 
    {
        try {
            Object streamerIdObj = cashService.retrieveData(STREAM_OWNER_KEY + streamId);
            return streamerIdObj != null ? streamerIdObj.toString() : null;
        } catch (Exception e) {
            System.err.println("Error getting streamer ID: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Close all connections for a stream
     */
    public void closeAllConnectionsForStream(String streamId) 
    {
        Set<String> sessions = getStreamSessionsFromCache(streamId);
        if (sessions != null && !sessions.isEmpty())
        {
            String streamerId = getStreamerId(streamId);
            String streamerName = getStreamerName(streamId);
            
            System.out.println("🔚 Closing all connections for stream: " + streamId + " (Total sessions: " + sessions.size() + ")");
            
            // Create stream end notification
            StreamEndNotificationDto streamEndNotification = new StreamEndNotificationDto(
                streamId,
                streamerId,
                streamerName,
                "STOPPED",
                LocalDateTime.now(),
                "The live stream has been ended",
                "STREAM_ENDED"
            );
            
            // Broadcast stream end notification to all viewers and streamer
            String streamEndTopic = "/topic/live-stream/" + streamId + "/stream-ended";
            System.out.println("📡 Broadcasting stream end notification to topic: " + streamEndTopic);
            
            try 
            {
                messagingTemplate.convertAndSend(streamEndTopic, streamEndNotification);
                System.out.println("Stream end notification broadcasted successfully");
            } 
            catch (Exception e) 
            {
                System.err.println("Error broadcasting stream end notification: " + e.getMessage());
                e.printStackTrace();
            }
            
            // Send multiple types of disconnection messages to ensure clients receive them
            for (String sessionId : sessions) 
            {
                SessionInfo sessionInfo = getSessionInfoFromCache(sessionId);
                if (sessionInfo != null) 
                {
                    try 
                    {
                        // Method 1: Send individual disconnection message to each session
                        String userDisconnectTopic = "/topic/live-stream/" + streamId + "/user-disconnect/" + sessionInfo.getUserId();
                        messagingTemplate.convertAndSend(userDisconnectTopic, streamEndNotification);
                        
                        // Method 2: Send session-specific force disconnect message
                        String sessionDisconnectTopic = "/topic/live-stream/session/" + sessionId + "/force-disconnect";
                        messagingTemplate.convertAndSend(sessionDisconnectTopic, "FORCE_DISCONNECT_SESSION");
                        
                        // Method 3: Send user-specific force disconnect (works even if they switch sessions)
                        String userForceDisconnectTopic = "/topic/live-stream/user/" + sessionInfo.getUserId() + "/force-disconnect";
                        messagingTemplate.convertAndSend(userForceDisconnectTopic, "FORCE_DISCONNECT_USER");
                        
                        // Method 4: Mark session as invalid in Redis for additional cleanup
                        cashService.storeData("invalid_session:" + sessionId, "STREAM_ENDED", TimeUnit.MINUTES.toMillis(5));
                        
                        System.out.println("Sent multiple disconnect signals to user: " + sessionInfo.getUsername() + " (" + sessionInfo.getUserId() + ") - Session: " + sessionId);
                    } 
                    catch (Exception e) 
                    {
                        System.err.println("Error sending disconnect message to user " + sessionInfo.getUserId() + ": " + e.getMessage());
                    }
                }
            }
            
            // Clear all sessions for this stream from cache
            cashService.deleteData(STREAM_SESSIONS_KEY + streamId);
            cashService.deleteData(STREAM_OWNER_KEY + streamId);
            cashService.deleteData(STREAMER_NAME_KEY + streamId);
            
            // Remove session details
            for (String sessionId : sessions) {
                cashService.deleteData(SESSION_DETAILS_KEY + sessionId);
            }
            
            // Remove from active streams
            Set<String> activeStreams = getActiveStreamsFromCache();
            activeStreams.remove(streamId);
            cashService.storeData(ACTIVE_STREAMS_KEY, activeStreams, SESSION_EXPIRATION);
            
            System.out.println("All sessions cleared for stream: " + streamId);
        }
        else 
        {
            System.out.println("No active sessions found for stream: " + streamId);
        }
    }
    
    /**
     * Get viewer count (excluding streamer)
     */
    public int getViewerCount(String streamId) 
    {
        Set<String> sessions = getStreamSessionsFromCache(streamId);
        if (sessions == null || sessions.isEmpty()) return 0;
        
        return (int) sessions.stream()
            .map(this::getSessionInfoFromCache)
            .filter(sessionInfo -> sessionInfo != null && !sessionInfo.isStreamer())
            .count();
    }
    
    /**
     * Get total active sessions count for a stream
     */
    public int getTotalSessionCount(String streamId) 
    {
        Set<String> sessions = getStreamSessionsFromCache(streamId);
        return sessions != null ? sessions.size() : 0;
    }
    
    /**
     * Get all active stream IDs
     */
    public Set<String> getAllActiveStreamIds() 
    {
        return getActiveStreamsFromCache();
    }
    
    /**
     * Check if stream has any active sessions
     */
    public boolean hasActiveSessions(String streamId) 
    {
        Set<String> sessions = getStreamSessionsFromCache(streamId);
        return sessions != null && !sessions.isEmpty();
    }
    
    /**
     * Check if a session is marked as invalid (e.g., stream ended)
     */
    public boolean isSessionInvalid(String sessionId) 
    {
        try 
        {
            Object invalidSession = cashService.retrieveData("invalid_session:" + sessionId);
            return invalidSession != null;
        } 
        catch (Exception e) 
        {
            System.err.println("Error checking session validity: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Validate session before allowing WebSocket operations
     * Should be called in WebSocket controllers before processing messages
     */
    public boolean validateSession(String sessionId, String streamId) 
    {
        // Check if session is marked as invalid
        if (isSessionInvalid(sessionId)) 
        {
            System.out.println("⚠️ Session " + sessionId + " is marked as invalid");
            return false;
        }
        
        // Check if session exists in our cache
        SessionInfo sessionInfo = getSessionInfoFromCache(sessionId);
        if (sessionInfo == null) 
        {
            System.out.println("⚠️ Session " + sessionId + " not found in cache");
            return false;
        }
        
        // Check if stream ID matches
        if (streamId != null && !streamId.equals(sessionInfo.getStreamId())) 
        {
            System.out.println("⚠️ Session " + sessionId + " stream ID mismatch. Expected: " + streamId + ", Found: " + sessionInfo.getStreamId());
            return false;
        }
        
        return true;
    }
    
    // Helper methods for cache operations
    
    @SuppressWarnings("unchecked")
    private Set<String> getStreamSessionsFromCache(String streamId) {
        try 
        {
            Object sessionData = cashService.retrieveData(STREAM_SESSIONS_KEY + streamId);
            if (sessionData != null) 
            {
                if (sessionData instanceof Set) 
                {
                    return (Set<String>) sessionData;
                } 
                else 
                {
                    // Try to convert from JSON if it's stored as string
                    return objectMapper.convertValue(sessionData, new TypeReference<Set<String>>() {});
                }
            }
        } 
        catch (Exception e) 
        {
            System.err.println("Error retrieving stream sessions from cache: " + e.getMessage());
        }
        return new HashSet<>();
    }
    
    private SessionInfo getSessionInfoFromCache(String sessionId) 
    {
        try
        {
            Object sessionData = cashService.retrieveData(SESSION_DETAILS_KEY + sessionId);
            if (sessionData != null) 
            {
                if (sessionData instanceof SessionInfo) 
                {
                    return (SessionInfo) sessionData;
                } 
                else 
                {
                    // Try to convert from JSON if it's stored as map/string
                    return objectMapper.convertValue(sessionData, SessionInfo.class);
                }
            }
        } catch (Exception e) {
            System.err.println("Error retrieving session info from cache: " + e.getMessage());
        }
        return null;
    }
    
    private String getStreamerName(String streamId) 
    {
        try 
        {
            Object streamerNameObj = cashService.retrieveData(STREAMER_NAME_KEY + streamId);
            return streamerNameObj != null ? streamerNameObj.toString() : null;
        } 
        catch (Exception e) 
        {
            System.err.println("Error getting streamer name: " + e.getMessage());
            return null;
        }
    }
    
    @SuppressWarnings("unchecked")
    private Set<String> getActiveStreamsFromCache() 
    {
        try 
        {
            Object activeStreamsData = cashService.retrieveData(ACTIVE_STREAMS_KEY);
            if (activeStreamsData != null) 
            {
                if (activeStreamsData instanceof Set)
                {
                    return (Set<String>) activeStreamsData;
                } 
                else 
                {
                    return objectMapper.convertValue(activeStreamsData, new TypeReference<Set<String>>() {});
                }
            }
        } 
        catch (Exception e) 
        {
            System.err.println("Error retrieving active streams from cache: " + e.getMessage());
        }
        return new HashSet<>();
    }
} 