package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.Mood;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.dtos.DropDownUserDto;
import com.hb.crm.core.searchBeans.SearchUser;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface UserRepository extends MongoRepository<User, String> {

    Optional<User> findByUsername(String username);

    Optional<User> findByUsernameAndUsertype(String username, UserType userType);

    List<User> findByCity(String city);

    Page<User> findByUsertypeIn(List<UserType> userType, Pageable pageable);

    Optional<User> findByPassResetKey(String passkey);

    @Query(value = "{ 'userInfo.email': ?0 }", exists = true)
    boolean existsByUserinfoEmail(String email);

    @Query(value = "{ 'userInfo.email': ?0 }")
    User getUserByEmail(String email);

    @Aggregation(pipeline = {
            "{ $match: { userType: 'Traveler' } }",
            "{ $project: { " +
                    "id: 1, " +
                    "username: 1, " +
                    "userInfo: 1, " +
                    "userType: 1 " +
                    "} }"
    })
    List<DropDownUserDto> findAllTravelers();

    List<User> findByMoodsIn(List<Mood> moods);

    List<User> findByIdIn(List<String> ids);

    /**
     * Find users by list of IDs with pagination support
     */
    Page<User> findByIdIn(List<String> ids, Pageable pageable);

    Page<User> findByIdInAndUsertype(List<String> ids, UserType type, Pageable pageable);

    List<User> findByFcmTokensContaining(String fcmToken);
}
