package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.City;
import com.hb.crm.core.beans.Country;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface CityRepository extends MongoRepository<City, String> {
    List<City> findCitiesByCountryId(String countryId);
    List<City> findByCountry(Country country);
    Optional<City> findFirstByNameIgnoreCase(String name);
    List<City> findAllByNameIgnoreCase(String name);

    @Query("{ 'name': { $regex: ?0, $options: 'i' }}")
    List<City> findByNameRegex(String searchTerm);
}
