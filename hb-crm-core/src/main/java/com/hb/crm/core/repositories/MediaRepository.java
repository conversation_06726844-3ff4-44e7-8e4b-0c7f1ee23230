package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.beans.Media;
import com.hb.crm.core.dtos.MediaWithUserDto;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface MediaRepository extends MongoRepository<Media, String> {


    List<Media> findAllByMediaType(MediaType mediaType);
    /**
     * Retrieves reels with associated user information for specified user IDs.
     * <p>
     * This aggregation performs the following steps:
     * <ol>
     *     <li>Matches reels where userId exists and is in the provided list</li>
     *     <li>Converts userId string to ObjectId for proper joining</li>
     *     <li>Performs a lookup to join with the user collection</li>
     *     <li>Unwraps the user array</li>
     *     <li>Projects the fields into the required format including user details</li>
     * </ol>
     *
     * @param userIds List of user IDs whose reels should be retrieved
     * @return List of MediaWithUserDto containing reels and their associated user information
     */
    @Aggregation(pipeline = {
            // 1. Match Stage: Filter reels by user IDs
            "{ $match: { mediaType: 'reel', userId: { $in: ?0, $ne: null } } }",

            // 2. Convert userId to ObjectId
            "{ $addFields: { userId: { $toObjectId: '$userId' } } }",

            // 3. Lookup users
            "{ $lookup: { from: 'user', localField: 'userId', foreignField: '_id', as: 'user' } }",

            // 4. Unwind user array
            "{ $unwind: { path: '$user' } }",

            // 5. Project fields
            "{ $project: { " +
                    "'id': '$_id', " +
                    "'title': '$title', " +
                    "'creationDate': '$creationDate', " +
                    "'lastUpdate': '$lastUpdate', " +
                    "'source': '$source', " +
                    "'description': '$description', " +
                    "'videoUrl': '$videoUrl', " +
                    "'imageCategory': '$imageCategory', " +
                    "'videoDuration': '$videoDuration', " +
                    "'videoDurationMS': '$videoDurationMS', " +
                    "'thumbnailClipUrl': '$thumbnailClipUrl', " +
                    "'thumbnailCaptureUrl': '$thumbnailCaptureUrl', " +
                    "'mediaType': '$mediaType', " +
                    "'OwnerId': '$OwnerId', " +
                    "'videoSize': '$videoSize', " +
                    "'LastUpdaterId': '$LastUpdaterId', " +
                    "'employee': '$employee', " +
                    "'numberOfReactions': '$numberOfReactions', " +
                    "'numberOfComments': '$numberOfComments', " +
                    "'userId': '$userId', " +
                    "'user': { " +
                    "    'id': { '$toString': '$user._id' }, " +
                    "    'profileImage': '$user.profileImage', " +
                    "    'firstName': '$user.firstName', " +
                    "    'lastName': '$user.lastName', " +
                    "    'about': '$user.about', " +
                    "    'username': '$user.username' " +
                    "} " +
                    "} }"
    })
    List<MediaWithUserDto> findReelsByUserIdIn(List<String> userIds);



    /**
     * Retrieves a random selection of reels with associated user information, excluding specified IDs.
     * <p>
     * This aggregation performs the following steps:
     * <ol>
     *     <li>Matches reels where userId is not null and excludes specified IDs</li>
     *     <li>Converts userId string to ObjectId for proper joining</li>
     *     <li>Performs a lookup to join with the user collection</li>
     *     <li>Unwraps the user array</li>
     *     <li>Projects the fields into the required format</li>
     *     <li>Adds a random order field for randomization</li>
     *     <li>Sorts by the random order</li>
     *     <li>Limits the result to the specified size</li>
     * </ol>
     *
     * @param size        The number of random reels to retrieve
     * @param excludedIds List of ObjectIds to exclude from the result
     * @return List of MediaWithUserDto containing randomly selected reels and their associated user information
     */
 @Aggregation(pipeline = {
    // 1. Match reels and exclude specific IDs
    "{ $match: { mediaType: 'reel', user: { $ne: null }, _id: { $nin: ?1 } } }",

    // 2. Lookup users
    "{ $lookup: { " +
        "from: 'user', " +
        "localField: 'user.$id', " +
        "foreignField: '_id', " +
        "as: 'user' " +
    "} }",

    // 3. Unwind user
    "{ $unwind: { path: '$user' } }",

    // 4. Lookup tags
    "{ $lookup: { " +
        "from: 'tag', " +
        "localField: 'tags.$id', " +
        "foreignField: '_id', " +
        "as: 'tags' " +
    "} }",

    // 5. Lookup post
    "{ $lookup: { " +
        "from: 'post', " +
        "localField: 'post.$id', " +
        "foreignField: '_id', " +
        "as: 'post' " +
    "} }",
    "{ $unwind: { path: '$post', preserveNullAndEmptyArrays: true } }",

    // 6. Lookup package
    "{ $lookup: { " +
        "from: 'package', " +
        "localField: '_package.$id', " +
        "foreignField: '_id', " +
        "as: '_package' " +
    "} }",
    "{ $unwind: { path: '$_package', preserveNullAndEmptyArrays: true } }",

    // 7. Project fields
    "{ $project: { " +
        "'id': '$_id', " +
        "'title': '$title', " +
        "'creationDate': '$creationDate', " +
        "'lastUpdate': '$lastUpdate', " +
        "'source': '$source', " +
        "'description': '$description', " +
        "'videoUrl': '$videoUrl', " +
        "'imageCategory': '$imageCategory', " +
        "'videoDuration': '$videoDuration', " +
        "'videoDurationMS': '$videoDurationMS', " +
        "'thumbnailClipUrl': '$thumbnailClipUrl', " +
        "'thumbnailCaptureUrl': '$thumbnailCaptureUrl', " +
        "'mediaType': '$mediaType', " +
        "'OwnerId': '$OwnerId', " +
        "'videoSize': '$videoSize', " +
        "'LastUpdaterId': '$LastUpdaterId', " +
        "'employee': '$employee', " +
        "'numberOfReactions': '$numberOfReactions', " +
        "'numberOfComments': '$numberOfComments', " +
        "'userId': '$userId', " +

        // user mapping
        "'user': { " +
            "'id': { '$toString': '$user._id' }, " +
            "'profileImage': '$user.profileImage', " +
            "'firstName': '$user.firstName', " +
            "'lastName': '$user.lastName', " +
            "'usertype': '$user.usertype', " +
            "'about': '$user.about', " +
            "'username': '$user.username' " +
        "}, " +

        // tags mapping
        "'tags': { " +
            "$map: { " +
                "input: '$tags', " +
                "as: 'tag', " +
                "in: { " +
                    "'id': { '$toString': '$$tag._id' }, " +
                    "'text': '$$tag.text' " +
                "} " +
            "} " +
        "}, " +

        // post mapping
        "'postOrStory': { " +
            "'id': { '$toString': '$post._id' }, " +
            "'text': '$post.text', " +
            "'slug': '$post.slug', " +
            "'description': '$post.description', " +
            "'type': '$post.postType' " +
            "}, " +

        // package mapping
        "'_package': { " +
            "'id': { '$toString': '$_package._id' }, " +
            "'name': '$_package.name', " +
            "'slug': '$_package.slug', " +
            "'description': '$_package.description' " +
        "}" +
    "} }",

    // 8. Add random order
    "{ $addFields: { randomOrder: { $rand: {} } } }",

    // 9. Sort randomly
    "{ $sort: { randomOrder: 1 } }",

    // 10. Limit
    "{ $limit: ?0 }"
})
List<MediaWithUserDto> findRandomReelsWithUser(int size, List<ObjectId> excludedIds);





    /**
     * Retrieves a media item along with its associated user information by the media ID.
     * <p>
     * This aggregation performs the following steps:
     * <ol>
     *     <li>Matches the media document by its ID</li>
     *     <li>Converts the userId string to ObjectId for proper joining</li>
     *     <li>Performs a lookup to join with the user collection</li>
     *     <li>Unwraps the user array (converts array to single object)</li>
     *     <li>Projects the fields into the required MediaWithUserDto format</li>
     * </ol>
     *
     * @param id The ObjectId of the media item to retrieve
     * @return A MediaWithUserDto containing both media and associated user information
     */
    @Aggregation(pipeline = {
        // 1. Match media by ID
        "{ $match: { _id: { $eq: ?0 } } }",

      // 2. Lookup users
    "{ $lookup: { " +
        "from: 'user', " +
        "localField: 'user.$id', " +
        "foreignField: '_id', " +
        "as: 'user' " +
    "} }",

    // 3. Unwind user
    "{ $unwind: { path: '$user' } }",

    // 4. Lookup tags
    "{ $lookup: { " +
        "from: 'tag', " +
        "localField: 'tags.$id', " +
        "foreignField: '_id', " +
        "as: 'tags' " +
    "} }",

    // 5. Lookup post
    "{ $lookup: { " +
        "from: 'post', " +
        "localField: 'post.$id', " +
        "foreignField: '_id', " +
        "as: 'post' " +
    "} }",
    "{ $unwind: { path: '$post', preserveNullAndEmptyArrays: true } }",

    // 6. Lookup package
    "{ $lookup: { " +
        "from: 'package', " +
        "localField: '_package.$id', " +
        "foreignField: '_id', " +
        "as: '_package' " +
    "} }",
    "{ $unwind: { path: '$_package', preserveNullAndEmptyArrays: true } }",

    // 7. Project fields
    "{ $project: { " +
        "'id': '$_id', " +
        "'title': '$title', " +
        "'creationDate': '$creationDate', " +
        "'lastUpdate': '$lastUpdate', " +
        "'source': '$source', " +
        "'description': '$description', " +
        "'videoUrl': '$videoUrl', " +
        "'imageCategory': '$imageCategory', " +
        "'videoDuration': '$videoDuration', " +
        "'videoDurationMS': '$videoDurationMS', " +
        "'thumbnailClipUrl': '$thumbnailClipUrl', " +
        "'thumbnailCaptureUrl': '$thumbnailCaptureUrl', " +
        "'mediaType': '$mediaType', " +
        "'OwnerId': '$OwnerId', " +
        "'videoSize': '$videoSize', " +
        "'LastUpdaterId': '$LastUpdaterId', " +
        "'employee': '$employee', " +
        "'numberOfReactions': '$numberOfReactions', " +
        "'numberOfComments': '$numberOfComments', " +
        "'userId': '$userId', " +

        // user mapping
        "'user': { " +
            "'id': { '$toString': '$user._id' }, " +
            "'profileImage': '$user.profileImage', " +
            "'firstName': '$user.firstName', " +
            "'lastName': '$user.lastName', " +
            "'usertype': '$user.usertype', " +
            "'about': '$user.about', " +
            "'username': '$user.username' " +
        "}, " +

        // tags mapping
        "'tags': { " +
            "$map: { " +
                "input: '$tags', " +
                "as: 'tag', " +
                "in: { " +
                    "'id': { '$toString': '$$tag._id' }, " +
                    "'text': '$$tag.text' " +
                "} " +
            "} " +
        "}, " +

        // post mapping
        "'postOrStory': { " +
             "'postOrStory_id': { '$toString': '$post._id' }, " +
            "'text': '$post.text', " +
            "'slug': '$post.slug', " +
            "'description': '$post.description', " +
            "'type': '$post.postType' " +
            "}, " +

        // package mapping
        "'_package': { " +
            "'id': { '$toString': '$_package._id' }, " +
            "'name': '$_package.name', " +
            "'slug': '$_package.slug', " +
            "'description': '$_package.description' " +
        "}" +
    "} }",
    })
    MediaWithUserDto findByIdWithUser(ObjectId id);



    long countByMediaType(MediaType mediaType);


    /**
     * Finds random reels that match the provided search query.
     * <p>
     * This aggregation performs the following steps:<br><br>
     * 1. **Search Stage:** Uses a `$search` query to find matching documents
     *    by searching within the `title` and `description` fields.<br><br>
     * 2. **Match Stage:** Filters the results to include only those with `mediaType` set to `'reel'`.<br>
     *
     * @param query The search query string to match against `title` and `description`.
     * @return A list of randomly selected media objects that match the criteria.
     */
 @Aggregation(pipeline = {
    // 1. Atlas Search on title & description
    "{ $search: { " +
        "'index': 'default', " +
        "'compound': { " +
            "'should': [ " +
                "{ 'autocomplete': { 'query': ?0, 'path': 'title' } }, " +
                "{ 'autocomplete': { 'query': ?0, 'path': 'description' } } " +
            "], " +
            "'minimumShouldMatch': 1 " +
        "} " +
    "} }",

    // 2. Match filter (no more userId)
    "{ $match: { mediaType: 'reel', user: { $ne: null } } }",

    // 3. Lookup user using DBRef
    "{ $lookup: { from: 'user', localField: 'user.$id', foreignField: '_id', as: 'user' } }",
    "{ $unwind: { path: '$user', preserveNullAndEmptyArrays: true } }",

    // 4. Lookup tags
    "{ $lookup: { from: 'tag', localField: 'tags.$id', foreignField: '_id', as: 'tags' } }",

    // 5. Lookup post
    "{ $lookup: { from: 'post', localField: 'post.$id', foreignField: '_id', as: 'post' } }",
    "{ $unwind: { path: '$post', preserveNullAndEmptyArrays: true } }",

    // 6. Lookup package
    "{ $lookup: { from: 'package', localField: '_package.$id', foreignField: '_id', as: '_package' } }",
    "{ $unwind: { path: '$_package', preserveNullAndEmptyArrays: true } }",

    // 7. Project result
    "{ $project: { " +
        "'id': '$_id', " +
        "'title': '$title', " +
        "'creationDate': '$creationDate', " +
        "'lastUpdate': '$lastUpdate', " +
        "'source': '$source', " +
        "'description': '$description', " +
        "'videoUrl': '$videoUrl', " +
        "'imageCategory': '$imageCategory', " +
        "'videoDuration': '$videoDuration', " +
        "'videoDurationMS': '$videoDurationMS', " +
        "'thumbnailClipUrl': '$thumbnailClipUrl', " +
        "'thumbnailCaptureUrl': '$thumbnailCaptureUrl', " +
        "'mediaType': '$mediaType', " +
        "'OwnerId': '$OwnerId', " +
        "'videoSize': '$videoSize', " +
        "'LastUpdaterId': '$LastUpdaterId', " +
        "'employee': '$employee', " +
        "'numberOfReactions': '$numberOfReactions', " +
        "'numberOfComments': '$numberOfComments', " +

        // Map user
        "'user': { " +
            "'id': { '$toString': '$user._id' }, " +
            "'profileImage': '$user.profileImage', " +
            "'firstName': '$user.firstName', " +
            "'lastName': '$user.lastName', " +
            "'about': '$user.about', " +
            "'username': '$user.username' " +
        "}, " +

        // Map tags
        "'tags': { $map: { input: '$tags', as: 'tag', in: { " +
            "'id': { '$toString': '$$tag._id' }, " +
            "'text': '$$tag.text' } } }, " +

        // Map postOrStory
        "'postOrStory': { " +
            "'postOrStory_id': { '$toString': '$post._id' }, " +
            "'text': '$post.text', " +
            "'slug': '$post.slug', " +
            "'description': '$post.description', " +
            "'type': '$post.type' " +
        "}, " +

        // Map _package
        "'_package': { " +
            "'id': { '$toString': '$_package._id' }, " +
            "'name': '$_package.name', " +
            "'slug': '$_package.slug', " +
            "'description': '$_package.description' " +
        "}" +
    "} }"
})
List<MediaWithUserDto> SearchReels(String query);

    /**
     * Find media by list of IDs with pagination support
     */
    Page<Media> findByIdIn(List<String> ids, Pageable pageable);

    /**
     * Find media by list of IDs without pagination
     */
    List<Media> findByIdIn(List<String> ids);



}
