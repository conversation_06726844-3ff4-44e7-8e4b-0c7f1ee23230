package com.hb.crm.core.repositories;

import com.hb.crm.core.CombinedKeys.SubscribeKey;
import com.hb.crm.core.beans.PaymentTransaction;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface PaymentTransactionRepository extends MongoRepository<PaymentTransaction, String> {
    List<PaymentTransaction> findBySubscribeId(SubscribeKey subscribeId);

    @Query("{ 'subscribeId.user.id' : ?0 }")
    List<PaymentTransaction> findAllByUserId(String userId);

    Optional<PaymentTransaction> findByPaymentGatewayReference(String referenceId);
}
