package com.hb.crm.core.CombinedKeys;

import com.hb.crm.core.beans.Post;
import com.hb.crm.core.beans.User;
import org.springframework.data.mongodb.core.mapping.DBRef;

public class viewPostKey {
    @DBRef
    private User user;
    @DBRef
    private Post story;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Post getStory() {
        return story;
    }

    public void setStory(Post story) {
        this.story = story;
    }
}
