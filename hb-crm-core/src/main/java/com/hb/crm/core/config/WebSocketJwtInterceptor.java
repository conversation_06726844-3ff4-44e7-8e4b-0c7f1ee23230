package com.hb.crm.core.config;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseToken;
import com.hb.crm.core.CashService.CashService;
import com.hb.crm.core.CashService.UserClaimResult;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.repositories.UserRepository;

import lombok.RequiredArgsConstructor;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class WebSocketJwtInterceptor implements ChannelInterceptor {

    private final CashService cashService;
    private final UserRepository userRepository ;

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) 
    {
        SimpMessageHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, SimpMessageHeaderAccessor.class);
        
        if (accessor != null) 
        {
            String token = getTokenFromHeaders(accessor);
            if (StringUtils.hasText(token)) 
            {
                try 
                {
                    // First check cache
                    UserClaimResult cachedClaims = cashService.retrieveUserData(token);
                    if (cachedClaims != null) 
                    {
                        storeUserInfoInSession(accessor, cachedClaims);
                        return message;
                    }
                    
                    // Verify with Firebase
                    FirebaseToken decodedToken = FirebaseAuth.getInstance().verifyIdToken(token);
                    String appId = (String) decodedToken.getClaims().get("appId");
                    
                    if (appId != null) 
                    {
                        // User has appId in token (regular case)
                        Optional<User> userOpt = userRepository.findById(appId);
                        if (userOpt.isPresent()) 
                        {
                            User user = userOpt.get();
                            // Store user info in session
                            accessor.getSessionAttributes().put("userId", user.getId());
                            accessor.getSessionAttributes().put("username", user.getUsername());
                            // Get firebaseId from the decoded token
                            accessor.getSessionAttributes().put("firebaseId", decodedToken.getUid());
                        }
                    } 
                    else 
                    {
                        // Handle Google auth users without appId (like your second user)
                        String email = decodedToken.getEmail();
                        String firebaseUid = decodedToken.getUid();
                        
                        if (email != null) 
                        {
                            // Try to find user by email
                            com.hb.crm.core.beans.User user = userRepository.getUserByEmail(email);
                            if (user != null) {
                                // Store user info in session
                                accessor.getSessionAttributes().put("userId", user.getId());
                                accessor.getSessionAttributes().put("username", user.getUsername());
                                accessor.getSessionAttributes().put("firebaseId", firebaseUid);
                                
                                System.out.println("🔍 Found user by email: " + user.getId() + " for email: " + email);
                            } 
                            else 
                            {
                                System.out.println("⚠️ User not found for email: " + email);
                            }
                        } 
                        else 
                        {
                            System.out.println("⚠️ No appId or email found in token");
                        }
                    }
                } 
                catch (Exception e) 
                {
                    System.err.println("WebSocket JWT validation failed: " + e.getMessage());
                    e.printStackTrace();
                    // Don't block the message, just continue without auth
                }
            }
        }
        return message;
    }

    private String getTokenFromHeaders(SimpMessageHeaderAccessor accessor) 
    {
        // Try to get token from native headers first
        Object authHeader = accessor.getFirstNativeHeader("Authorization");
        
        if (authHeader instanceof String) 
        {
            String bearerToken = (String) authHeader;
            
            if (StringUtils.hasText(bearerToken)) 
            {
                // Handle double "Bearer" prefix
                if (bearerToken.startsWith("Bearer Bearer ")) 
                {
                    String token = bearerToken.substring(14); // Remove "Bearer Bearer "
                    return token;
                } 
                else if (bearerToken.startsWith("Bearer ")) 
                {
                    String token = bearerToken.substring(7); // Remove "Bearer "
                    return token;
                }
            }
        }
        
        // Fallback: try to get from session attributes (if passed during connect)
        Object token = accessor.getSessionAttributes().get("token");
        System.out.println("Token from session: " + token);
        return token instanceof String ? (String) token : null;
    }

    private void storeUserInfoInSession(SimpMessageHeaderAccessor accessor, UserClaimResult claims) {
        accessor.getSessionAttributes().put("userId", claims.getUserid());
        accessor.getSessionAttributes().put("username", claims.getEmail());
        accessor.getSessionAttributes().put("firebaseId", claims.getFireBaseUserId());
    }
}