package com.hb.crm.core.repositories.chat;

import com.hb.crm.core.Enums.chat.ChatMessageType;
import com.hb.crm.core.beans.chat.OneChat.ChatMessage;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ChatMessageRepository extends MongoRepository<ChatMessage, String> {
    
    // Get messages for a conversation with pagination and time filter
    @Query("{ 'conversation.id': ?0, 'deletedByMe': false }")
    Page<ChatMessage> findByConversationIdAndDateTimeBefore(
        String conversationId,
        Pageable pageable
    );


    @Query("{ 'conversation.id': ?0, 'deletedByMe': false, $or: [ { 'type': { $exists: false } }, { 'type': { $in: ?1 } } ] }")
    Page<ChatMessage> findByConversationIdAndTypesAndDateTimeBefore(
            String conversationId,
            List<ChatMessageType> types,
            Pageable pageable
    );


    // Get message by conversation ID and poll ID
    @Query("{ 'conversation.id': ?0, 'type': 'Poll', 'poll.id': ?1 }")
    Optional<ChatMessage> findByConversationIdAndPollId(String conversationId, String pollId);

    @Query("{ 'conversation.id': ?0, 'deletedByMe': false," +
            " 'user.id': { $ne: ?1 }, 'status': { $ne: 'Read' } }")
    long countUnreadMessagesByConversationAndUserId(String conversationId, String userId);

    @Query("{ 'deletedByMe': false, 'user.id': { $ne: ?0 }, 'status': { $ne: 'Read' } }")
    long countAllUnreadMessagesByUserId(String userId);

    @Query("{" +
           "'deletedByMe': false, " +
           "'text': { $regex: ?0, $options: 'i' }, " +
           "'dateTime': { $gte: ?1, $lte: ?2 } " +
           "}")
    Page<ChatMessage> searchMessagesWithDateFilter(
        String searchText,
        LocalDateTime startDate,
        LocalDateTime endDate,
        Pageable pageable
    );

    @Query("{" +
            "'deletedByMe': false, " +
            "'text': { $regex: ?0, $options: 'i' } " +
            "}")
    Page<ChatMessage> searchMessages(
            String searchText,
            Pageable pageable
    );

    long countByConversationIdAndStatusNotAndDeletedByMeFalse(String conversationId, String status);


    /**
     * Finds the most recent message in a conversation
     * 
     * @param conversationId the ID of the conversation
     * @return the most recent message in the conversation
     */
    ChatMessage findTopByConversationIdAndDeletedByMeFalseOrderByDateTimeDesc(String conversationId);
}
