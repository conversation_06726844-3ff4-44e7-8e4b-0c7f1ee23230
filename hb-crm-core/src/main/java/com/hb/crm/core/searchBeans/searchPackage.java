package com.hb.crm.core.searchBeans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.State;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class searchPackage extends BaseEntity {

    private String link;
    private PackageType packageType = PackageType.TravelWithMe;
    private String name;
    @JsonIgnore()
    private String packageId;
    private String description;
    private List<MediaWrapperSearch> medias;
    private PackageStatus packageStatus = PackageStatus.draft;
    private LocalDateTime start;
    private LocalDateTime end;
    private simpleUserInfo infulancer;
    private int capacity;
    private int subscribeCount;
    private List<Tag> tags;
    private boolean favouritebyme;
    private boolean NotficationEnabledByMe;
    private boolean loveIt;
    private int numberOfRoom;
    private List<Rate> rates;
    private List<Rate> followMeRates;
    private double AvgRate;
    private double followMeAvgRate;
//    private State state;
    private List<SearchMood> moods;
    private List<PackageCountry> packagePlaces;
    private BigDecimal totalPrice;
    private LocalDateTime creationDate;
    private String rejectionNote = "";
    private float latitude;
    private float longtuid;
    private float latitudeTo;
    private float longtuidTo;
    private String Place;
    private String ToPlace;
    private int availabileSeat;
    private boolean availableForFollowMe;
    private LocalDateTime availableFrom;
    private long duration;
    private LocalDateTime UpdateDate;
    private String slug;
    private int state;

    public State getState() {
        return State.fromValue(this.state);
    }

    public void setState(State state) {
        this.state = state.getValue();
    }

}