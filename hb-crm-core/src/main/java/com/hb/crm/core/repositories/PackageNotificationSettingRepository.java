package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.PackageNotificationSetting;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface PackageNotificationSettingRepository extends MongoRepository<PackageNotificationSetting, String> {

    @Query("{'_package.id': ?0}")
    PackageNotificationSetting findByPackageId(String packageId);
}
