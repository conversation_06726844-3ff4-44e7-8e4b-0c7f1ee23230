package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Package;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Collection;
import java.util.List;

public interface PackageRepository extends MongoRepository<Package, String> {
    List<Package> findByInfulancerId(String influencerId);

    @Aggregation(pipeline = {
            "{ $search: { " +
                    "index: 'autocomplete', " +
                    "compound: { " +
                    "  should: [ " +
                    "    { autocomplete: { query: ?0, path: 'name', fuzzy: { maxEdits: 2, prefixLength: 1 } } }, " +
                    "    { autocomplete: { query: ?0, path: 'description', fuzzy: { maxEdits: 2, prefixLength: 1 } } }, " +
                    "    { autocomplete: { query: ?0, path: 'slug', fuzzy: { maxEdits: 2, prefixLength: 1 } } }, " +
                    "    { autocomplete: { query: ?0, path: 'fromAirport.name', fuzzy: { maxEdits: 2, prefixLength: 1 } } }, " +
                    "    { autocomplete: { query: ?0, path: 'fromAirport.city', fuzzy: { maxEdits: 2, prefixLength: 1 } } }, " +
                    "    { autocomplete: { query: ?0, path: 'toAirport.name', fuzzy: { maxEdits: 2, prefixLength: 1 } } }, " +
                    "    { autocomplete: { query: ?0, path: 'toAirport.city', fuzzy: { maxEdits: 2, prefixLength: 1 } } } " +
                    "  ] " +
                    "} " +
                    "} } " +
                    "{" +
                    "  $match: {" +
                    "    infulancer.$id: ObjectId(?0)" +
                    "  }" +
                    "}" +
                    "{ $addFields: { score: { $meta: 'searchScore' } } }, " +
                    "{ $sort: { score: -1 } }"
    })
    List<Package> findBySearchTermAndInfluencerId(String searchTerm, String influencerId);

    List<Package> findAllById(Collection<String> ids);
}

