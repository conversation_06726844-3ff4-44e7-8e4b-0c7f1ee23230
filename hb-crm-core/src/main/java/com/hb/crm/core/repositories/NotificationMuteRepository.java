package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.beans.Notification.NotificationMute;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NotificationMuteRepository extends MongoRepository<NotificationMute, String> {
    
    /**
     * Find all notification mutes for a specific user
     */
    List<NotificationMute> findByUserId(String userId);
    
    /**
     * Find all notification mutes for a specific user with pagination
     */
    Page<NotificationMute> findByUserId(String userId, Pageable pageable);
    
    /**
     * Find all notification mutes for a specific user and entity type
     */
    List<NotificationMute> findByUserIdAndEntityType(String userId, NotificationEntityType entityType);
    
    /**
     * Find all notification mutes for a specific user and entity type with pagination
     */
    Page<NotificationMute> findByUserIdAndEntityType(String userId, NotificationEntityType entityType, Pageable pageable);
    
    /**
     * Find a specific notification mute by user, entity ID and entity type
     */
    Optional<NotificationMute> findByUserIdAndEntityIdAndEntityType(String userId, String entityId, NotificationEntityType entityType);
    
    /**
     * Check if a user mutes a specific entity
     */
    boolean existsByUserIdAndEntityIdAndEntityType(String userId, String entityId, NotificationEntityType entityType);
    
    /**
     * Delete all notification mutes for a specific user and entity type
     */
    void deleteByUserIdAndEntityType(String userId, NotificationEntityType entityType);
    
    /**
     * Delete a specific notification mute
     */
    void deleteByUserIdAndEntityIdAndEntityType(String userId, String entityId, NotificationEntityType entityType);
}
