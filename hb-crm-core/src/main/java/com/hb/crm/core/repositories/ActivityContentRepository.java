package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.ActivityContent;
import com.hb.crm.core.dtos.ActivityContentDto;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivityContentRepository extends MongoRepository<ActivityContent, String> {
    // Additional custom query methods if needed

    /**
     * Retrieves a list of activity content with only specific fields: id, name, and one media
     * (where {@code mainImage} is set to {@code true}). If no media has {@code mainImage = true},
     * the media field will be null.
     *
     * @return A list of {@link ActivityContentDto} containing id, name, and one main media.
     */

    @Aggregation(pipeline = {
            "{ $project: { " +
                    "id: 1, " +
                    "name: 1, " +
                    "media: { $arrayElemAt: [ " +
                    "{ $map: { " +
                    "input: { $filter: { " +
                    "input: '$medias', " +
                    "as: 'media', " +
                    "cond: { $eq: ['$$media.mainImage', true] } " +
                    "} }, " +
                    "as: 'media', " +
                    "in: { " +
                    "type: '$$media.type', " +
                    "caption: '$$media.caption', " +
                    "asset: '$$media.asset', " +
                    "url: '$$media.url', " +
                    "mainImage: '$$media.mainImage' " +
                    "} " +
                    "} }, " +
                    "0 ] } " +
                    "} }"
    })
    List<ActivityContentDto> findActivityContentWithMainImage();


    /**
     * Searches for activity content by name using a case-insensitive regex query.
     * Returns a list of activity content containing id, name, and one main media (if available).
     *
     * @param searchQuery The search term to match against the activity content name.
     * @return A list of {@link ActivityContentDto} matching the search query.
     */

    @Aggregation(pipeline = {
            "{ $match: { name: { $regex: ?0, $options: 'i' } } }",
            "{ $project: { " +
                    "id: 1, " +
                    "name: 1, " +
                    "media: { $arrayElemAt: [ " +
                    "{ $map: { " +
                    "input: { $filter: { " +
                    "input: '$medias', " +
                    "as: 'media', " +
                    "cond: { $eq: ['$$media.mainImage', true] } " +
                    "} }, " +
                    "as: 'media', " +
                    "in: { " +
                    "type: '$$media.type', " +
                    "caption: '$$media.caption', " +
                    "asset: '$$media.asset', " +
                    "url: '$$media.url', " +
                    "mainImage: '$$media.mainImage' " +
                    "} " +
                    "} }, " +
                    "0 ] } " +
                    "} }"
    })
    List<ActivityContentDto> searchByName(String searchQuery);
}