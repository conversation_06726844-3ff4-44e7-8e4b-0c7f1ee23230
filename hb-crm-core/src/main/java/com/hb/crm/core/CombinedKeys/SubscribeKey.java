package com.hb.crm.core.CombinedKeys;

import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.DBRef;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubscribeKey {
    @DBRef(lazy = true)
    @JsonIdentityReference(alwaysAsId = true)
    private User user = new User();

    @DBRef(lazy = true)
    @JsonIdentityReference(alwaysAsId = true)
    private SubPackage _package = new SubPackage();


    public SubscribeKey(String userId, String packageId) {

        this.user.setId(userId);
        this._package.setId(packageId);
    }
}
