package com.hb.crm.core.searchBeans;

import com.hb.crm.core.Enums.State;
import com.hb.crm.core.beans.ActivityCategory;
import com.hb.crm.core.beans.MediaWrapper;
import com.hb.crm.core.beans.Place;

import java.time.LocalDateTime;

public class activity {
    private  String name;
    private  String details;
    private LocalDateTime start;
    private  LocalDateTime end;
    private Place place;
    private MediaWrapper media;
    private  Object OtherDetail;
    private State state ;
    private ActivityCategory Category;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public LocalDateTime getStart() {
        return start;
    }

    public void setStart(LocalDateTime start) {
        this.start = start;
    }

    public LocalDateTime getEnd() {
        return end;
    }

    public void setEnd(LocalDateTime end) {
        this.end = end;
    }

    public Place getPlace() {
        return place;
    }

    public void setPlace(Place place) {
        this.place = place;
    }

    public MediaWrapper getMedia() {
        return media;
    }

    public void setMedia(MediaWrapper media) {
        this.media = media;
    }

    public Object getOtherDetail() {
        return OtherDetail;
    }

    public void setOtherDetail(Object otherDetail) {
        OtherDetail = otherDetail;
    }

    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    public ActivityCategory getCategory() {
        return Category;
    }

    public void setCategory(ActivityCategory category) {
        Category = category;
    }
}
