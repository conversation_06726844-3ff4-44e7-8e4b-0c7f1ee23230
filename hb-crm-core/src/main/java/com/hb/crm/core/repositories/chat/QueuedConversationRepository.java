package com.hb.crm.core.repositories.chat;

import com.hb.crm.core.beans.chat.QueuedConversation;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QueuedConversationRepository extends MongoRepository<QueuedConversation, String> {

    List<QueuedConversation> findByOrderByTimestampAsc();

    long count();
}