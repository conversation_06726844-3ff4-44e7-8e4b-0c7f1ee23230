package com.hb.crm.core.searchBeans;

import com.hb.crm.core.Enums.UserType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

@Setter
@Getter
public class simpleUserInfo {
    @Id
    private String Id;
    private String username;
    private String firstName;
    private String lastName;
    private UserType usertype;
    private String coverImage;
    private String profileImage;
}
