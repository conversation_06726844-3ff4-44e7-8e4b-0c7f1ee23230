package com.hb.crm.admin.config;

import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.beans.Notification.*;
import com.hb.crm.core.beans.Setting;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.repositories.chat.ConversationRepository;
import com.hb.crm.core.repositories.chat.GroupConversationRepository;
import com.hb.crm.core.repositories.chat.UserConversationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.hb.crm.admin.config.Constants.USER_STORIES_LIMIT_PER_USER;
import static com.hb.crm.admin.config.Constants.USER_STORIES_SETTING;

@Component
public class seeder implements CommandLineRunner {
    private final SettingRepository settingRepository;
    private final ConversationRepository conversationRepository;
    private final UserRepository userRepository;
    private final GroupConversationRepository groupConversationRepository;
    private final SubPackageRepository subPackageRepository;
    private final UserConversationRepository userConversationRepository;
    private final NotificationTemplateRepository notificationTemplateRepository;
    private final NotificationTemplateVariableRepository notificationTemplateVariableRepository;

    @Autowired
    public seeder(SettingRepository settingRepository, ConversationRepository conversationRepository, UserRepository userRepository, GroupConversationRepository groupConversationRepository, SubPackageRepository subPackageRepository, UserConversationRepository userConversationRepository, NotificationTemplateRepository notificationTemplateRepository, NotificationTemplateVariableRepository notificationTemplateVariableRepository) {
        this.settingRepository = settingRepository;
        this.conversationRepository = conversationRepository;
        this.userRepository = userRepository;
        this.groupConversationRepository = groupConversationRepository;
        this.subPackageRepository = subPackageRepository;
        this.userConversationRepository = userConversationRepository;
        this.notificationTemplateRepository = notificationTemplateRepository;
        this.notificationTemplateVariableRepository = notificationTemplateVariableRepository;
    }

    @Override
    public void run(String... args) {

        // Seed NotificationTemplate data
        seedNotificationTemplates();

        // Check if the settings collection is already populated
        if (settingRepository.findByName("MaxAgentQueueSizeForTransfer") == null) {
            // Seed initial settings data
            Setting setting1 = new Setting();
            setting1.setName("MaxAgentQueueSizeForTransfer");
            setting1.setValue("5");
            settingRepository.save(setting1);
        }
        if (settingRepository.findByName("MaxAgentQueueSizeForMainQueue") == null) {
            // Seed initial settings data
            Setting setting1 = new Setting();
            setting1.setName("MaxAgentQueueSizeForMainQueue");
            setting1.setValue("5");
            settingRepository.save(setting1);
        }
        if (settingRepository.findByName("MaxAgentOfflineTime") == null) {
            // Seed initial settings data
            Setting setting1 = new Setting();
            setting1.setName("MaxAgentOfflineTime");
            setting1.setValue("10");
            settingRepository.save(setting1);
        }
        if (settingRepository.findByName("MaxAgentDiscardingChatTime") == null) {
            // Seed initial settings data
            Setting setting1 = new Setting();
            setting1.setName("MaxAgentDiscardingChatTime");
            setting1.setValue("60");
            settingRepository.save(setting1);
        }
        if (settingRepository.findByName("MaxTopRatedNumber") == null) {
            // Seed initial settings data
            Setting setting1 = new Setting();
            setting1.setName("MaxTopRatedNumber");
            setting1.setValue("5");
            settingRepository.save(setting1);
        }
        if (settingRepository.findByName("whatsappNumber") == null) {
            Setting setting2 = new Setting();
            setting2.setName("whatsappNumber");
            setting2.setValue("5");
            settingRepository.save(setting2);
        }
        if (settingRepository.findByName("flightMaxCapacity") == null) {
            Setting setting3 = new Setting();
            setting3.setName("flightMaxCapacity");
            setting3.setValue("9");
            settingRepository.save(setting3);
        }
        if (settingRepository.findByName("AdminEmail") == null) {
            // Seed initial settings data
            Setting setting4 = new Setting();
            setting4.setName("AdminEmail");
            setting4.setValue("<EMAIL>");
            settingRepository.save(setting4);
        }
        if (settingRepository.findByName("TritiumSupport") == null) {
            // Seed initial settings data
            Setting setting5 = new Setting();
            setting5.setName("TritiumSupport");
            setting5.setValue("<EMAIL>");
            settingRepository.save(setting5);
        }
        if (settingRepository.findByName("OfflinePackageExpierDate") == null) {
            // Seed initial settings data
            Setting setting6 = new Setting();
            setting6.setName("OfflinePackageExpierDate");
            setting6.setValue("1");
            settingRepository.save(setting6);
        }
        if (settingRepository.findByName("EmailSubject") == null) {
            // Seed initial settings data
            Setting setting7 = new Setting();
            setting7.setName("EmailSubject");
            setting7.setValue("Hello Document");
            settingRepository.save(setting7);
        }
        if (settingRepository.findByName("EmailDescription") == null) {
            // Seed initial settings data
            Setting setting8 = new Setting();
            setting8.setName("EmailDescription");
            setting8.setValue("Hello your doc is here");
            settingRepository.save(setting8);
        }
        if (settingRepository.findByName("StoryCheckLifeTime") == null) {
            // Seed initial settings data
            Setting setting9 = new Setting();
            setting9.setName("StoryCheckLifeTime");
            setting9.setValue("true");
            settingRepository.save(setting9);
        }

        if (settingRepository.findByName("ClipsNumber") == null) {
            // Seed initial settings data
            Setting setting9 = new Setting();
            setting9.setName("ClipsNumber");
            setting9.setValue("10");
            settingRepository.save(setting9);
        }
        if (settingRepository.findByName("MinimumIntervalBetweenClips") == null) {
            // Seed initial settings data
            Setting setting10 = new Setting();
            setting10.setName("MinimumIntervalBetweenClips");
            setting10.setValue("1000");
            settingRepository.save(setting10);
        }
        if (settingRepository.findByName("clipDuration") == null) {
            // Seed initial settings data
            Setting setting11 = new Setting();
            setting11.setName("clipDuration");
            setting11.setValue("3000");
            settingRepository.save(setting11);
        }
        if (settingRepository.findByName(USER_STORIES_SETTING) == null) {
            Setting setting12 = new Setting();
            setting12.setName(USER_STORIES_SETTING);
            setting12.setValue("");
            settingRepository.save(setting12);
        }

        if (settingRepository.findByName(USER_STORIES_LIMIT_PER_USER) == null) {
            Setting setting13 = new Setting();
            setting13.setName(USER_STORIES_LIMIT_PER_USER);
            setting13.setValue("10");
            settingRepository.save(setting13);
        }
    }

    private void seedNotificationTemplates() {
        // Check if notification templates are already seeded
        if (notificationTemplateRepository.count() > 0) {
            return;
        }

        // Create notification templates for each NotificationType with their variables

        createNotificationTemplate(NotificationType.SuccessLoginAdmin,
                "New Succeeded Login",
                "You logged into your admin account successfully at {{timestamp}} from IP {{ipAddress}}.",
                null,
                List.of(NotificationChannelType.Email),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)));

        createNotificationTemplate(NotificationType.FailedLoginAdmin,
                "Failed login attempt",
                "There was a failed login attempt to your admin account at {{timestamp}} from IP {{ip_address}}.",
                null,
                List.of(NotificationChannelType.Email),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedLoginAdmin), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)));

        createNotificationTemplate(NotificationType.SuccessPasswordChange,
                "Password changed",
                "Your admin account password was changed successfully at {{timestamp}}.",
                null,
                List.of(NotificationChannelType.Email),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.SuccessPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)));


        createNotificationTemplate(NotificationType.FailedPasswordChange,
                "Password Change Attempt Failed",
                "An attempt to change your admin account password failed at {{timestamp}}.",
                null,
                List.of(NotificationChannelType.Email),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("timestamp", "Time stamp", "LoginNotificationTemplateDto", NotificationType.FailedPasswordChange), createTemplateVariable("ipAddress", "Ip Address", "LoginNotificationTemplateDto", NotificationType.MatchedMoodsNewPackage)));

        createNotificationTemplate(NotificationType.LiveStarted,
                "Live Started!",
                "{{username}} started a live session at {{timestamp}}",
                null,
                List.of(NotificationChannelType.Push),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.LiveStarted)),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.LiveStarted)),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.LiveStarted)),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.LiveStarted)),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.LiveStarted)));


        createNotificationTemplate(NotificationType.SubscriptionCanceled,
                "Subscription Cancelled!",
                "Subscription for package {{name}} successfully canceled",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                List.of(NotificationChannelType.Email),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubscriptionCanceled)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubscriptionCanceled)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubscriptionCanceled)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubscriptionCanceled)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubscriptionCanceled)));





        createNotificationTemplate(NotificationType.MatchedMoodsNewPackage,
                "New Package Matches Your Mood",
                "A new package {{name}} matches your interests!",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                List.of(NotificationChannelType.Push),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.MatchedMoodsNewPackage)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.MatchedMoodsNewPackage)));

        createNotificationTemplate(NotificationType.FollowedInfluencerNewPackage,
                "New Package from Followed Influencer",
                "{{username}} has created a new package: {{name}}",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.FollowedInfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.FollowedInfluencerNewPackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.FollowedInfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.FollowedInfluencerNewPackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.FollowedInfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.FollowedInfluencerNewPackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.FollowedInfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.FollowedInfluencerNewPackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.FollowedInfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.FollowedInfluencerNewPackage)));

        createNotificationTemplate(NotificationType.InfluencerNewPackage,
                "New Package Available",
                "{{username}} has created a new package: {{name}}",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerNewPackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerNewPackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerNewPackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerNewPackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerNewPackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerNewPackage)));

        createNotificationTemplate(NotificationType.InfluencerUpdatePackage,
                "Package Updated",
                "{{username}} has updated the package: {{name}}",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerUpdatePackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerUpdatePackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerUpdatePackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerUpdatePackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerUpdatePackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerUpdatePackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerUpdatePackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerUpdatePackage)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.InfluencerUpdatePackage), createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.InfluencerUpdatePackage)));

        createNotificationTemplate(NotificationType.SubmittedSubscribe,
                "Subscription Submitted",
                "Your subscription to {{name}} has been submitted for approval",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                Arrays.asList(NotificationChannelType.Push, NotificationChannelType.Email),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubmittedSubscribe)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubmittedSubscribe)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubmittedSubscribe)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubmittedSubscribe)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.SubmittedSubscribe)));

        createNotificationTemplate(NotificationType.PackageCapacitySubscribedUsers,
                "Package Capacity Alert",
                "Package {{name}} is reaching its capacity limit",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                Arrays.asList(NotificationChannelType.Push, NotificationChannelType.Email),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacitySubscribedUsers)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacitySubscribedUsers)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacitySubscribedUsers)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacitySubscribedUsers)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacitySubscribedUsers)));

        createNotificationTemplate(NotificationType.PackageCapacityFavouriteUsers,
                "Package Capacity Alert",
                "Your favourite package {{name}} is reaching its capacity limit!",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                Arrays.asList(NotificationChannelType.Push, NotificationChannelType.Email),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacityFavouriteUsers)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacityFavouriteUsers)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacityFavouriteUsers)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacityFavouriteUsers)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PackageCapacityFavouriteUsers)));

        createNotificationTemplate(NotificationType.CommentedOnLikedPost,
                "Comment on Liked Post",
                "{{username}} commented on a post you liked: {{comment}}",
                "imgs/icon/2025/06/20/afe4fb12-3c86-49b0-a2ec-e36d51e9e512.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnLikedPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnLikedPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnLikedPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnLikedPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnLikedPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnLikedPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnLikedPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnLikedPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnLikedPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnLikedPost)));

        createNotificationTemplate(NotificationType.CommentedOnMyPost,
                "New Comment on Your Post",
                "{{username}} commented on your post: {{comment}}",
                "💬",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnMyPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnMyPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnMyPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnMyPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.CommentedOnMyPost), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.CommentedOnMyPost)));

        createNotificationTemplate(NotificationType.ReactOnLikedPost,
                "Reaction on Liked Post",
                "{{username}} reacted to a post you liked",
                "imgs/icon/2025/06/20/afe4fb12-3c86-49b0-a2ec-e36d51e9e512.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedPost)));

        createNotificationTemplate(NotificationType.ReactOnLikedStory,
                "Reaction on Liked Story",
                "{{username}} reacted to a story you liked",
                "imgs/icon/2025/06/20/afe4fb12-3c86-49b0-a2ec-e36d51e9e512.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedStory)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedStory)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedStory)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedStory)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnLikedStory)));

        createNotificationTemplate(NotificationType.ReactOnMyComment,
                "Reaction on Your Comment",
                "{{username}} reacted to your comment {{comment}}",
                "imgs/icon/2025/06/20/afe4fb12-3c86-49b0-a2ec-e36d51e9e512.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyComment), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.ReactOnMyComment)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyComment), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.ReactOnMyComment)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyComment), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.ReactOnMyComment)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyComment), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.ReactOnMyComment)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyComment), createTemplateVariable("comment", "Comment Text", "Comment", NotificationType.ReactOnMyComment)));

        createNotificationTemplate(NotificationType.ReactOnMyPost,
                "Reaction on Your Post",
                "{{username}} reacted to your post",
                "imgs/icon/2025/06/20/afe4fb12-3c86-49b0-a2ec-e36d51e9e512.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)));


        createNotificationTemplate(NotificationType.ReactOnMyReel,
                "Reaction on Your Reel",
                "{{username}} reacted to your reel",
                "imgs/icon/2025/06/20/afe4fb12-3c86-49b0-a2ec-e36d51e9e512.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyPost)));


        createNotificationTemplate(NotificationType.ReactOnMyStory,
                "Reaction on Your Story",
                "{{username}} reacted to your story",
                "imgs/icon/2025/06/20/afe4fb12-3c86-49b0-a2ec-e36d51e9e512.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyStory)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyStory)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyStory)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyStory)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReactOnMyStory)));

        createNotificationTemplate(NotificationType.Follow,
                "New Follower",
                "{{username}} started following you",
                "👤",
                List.of(NotificationChannelType.Push),
                List.of(createTemplateVariable("username", "Follower Name", "User", NotificationType.Follow)),
                List.of(createTemplateVariable("username", "Follower Name", "User", NotificationType.Follow)),
                List.of(createTemplateVariable("username", "Follower Name", "User", NotificationType.Follow)),
                List.of(createTemplateVariable("username", "Follower Name", "User", NotificationType.Follow)),
                List.of(createTemplateVariable("username", "Follower Name", "User", NotificationType.Follow)));

        createNotificationTemplate(NotificationType.PaymentSucceeded,
                "Payment Successful",
                "Your payment for {{name}} has been processed successfully",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                Arrays.asList(NotificationChannelType.Push, NotificationChannelType.Email),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PaymentSucceeded)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PaymentSucceeded)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PaymentSucceeded)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PaymentSucceeded)),
                List.of(createTemplateVariable("name", "Package Name", "SubPackage", NotificationType.PaymentSucceeded)));
//
//        createNotificationTemplate(NotificationType.PaymentSucceeded,
//                "Travel With Me - Success",
//                "Your Travel With Me booking has been confirmed successfully",
//                "✈️",
//                Arrays.asList(NotificationChannelType.Push, NotificationChannelType.Email),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceeded)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceeded)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceeded)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceeded)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceeded)));
//
//        createNotificationTemplate(NotificationType.PaymentSucceeded,
//                "Travel With Me V2 - Success",
//                "Your Travel With Me V2 booking has been confirmed successfully",
//                "🌍",
//                Arrays.asList(NotificationChannelType.Push, NotificationChannelType.Email),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceededV2)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceededV2)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceededV2)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceededV2)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.TravelWithMeSucceededV2)));
//
//        createNotificationTemplate(NotificationType.PaymentSucceeded,
//                "Success Notification",
//                "Operation completed successfully",
//                "✅",
//                List.of(NotificationChannelType.Push),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.Success2)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.Success2)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.Success2)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.Success2)),
//                List.of(createTemplateVariable("message", "Success Message", "String", NotificationType.Success2)));

        createNotificationTemplate(NotificationType.ReplyComment,
                "Reply to Comment",
                "{{username}} replied to your comment: {{comment}}",
                "↩️",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReplyComment), createTemplateVariable("comment", "Reply Text", "Reply", NotificationType.ReplyComment)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReplyComment), createTemplateVariable("comment", "Reply Text", "Reply", NotificationType.ReplyComment)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReplyComment), createTemplateVariable("comment", "Reply Text", "Reply", NotificationType.ReplyComment)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReplyComment), createTemplateVariable("comment", "Reply Text", "Reply", NotificationType.ReplyComment)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.ReplyComment), createTemplateVariable("comment", "Reply Text", "Reply", NotificationType.ReplyComment)));

        createNotificationTemplate(NotificationType.NewStoryFromFollowedInfluencer,
                "New Story from Followed Influencer",
                "{{username}} shared a new story",
                "📸",
                List.of(NotificationChannelType.Push),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewStoryFromFollowedInfluencer)),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewStoryFromFollowedInfluencer)),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewStoryFromFollowedInfluencer)),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewStoryFromFollowedInfluencer)),
                List.of(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewStoryFromFollowedInfluencer)));

        createNotificationTemplate(NotificationType.NewPostFromFollowedInfluencer,
                "New Post from Followed Influencer",
                "{{username}} shared a new post: {{text}}",
                "📄",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewPostFromFollowedInfluencer), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFollowedInfluencer)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewPostFromFollowedInfluencer), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFollowedInfluencer)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewPostFromFollowedInfluencer), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFollowedInfluencer)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewPostFromFollowedInfluencer), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFollowedInfluencer)),
                Arrays.asList(createTemplateVariable("username", "Influencer Name", "User", NotificationType.NewPostFromFollowedInfluencer), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFollowedInfluencer)));

        createNotificationTemplate(NotificationType.NewPostFromFavouritePackageUser,
                "New Post from Favourite Package User",
                "{{username}} shared a new post in your favourite package: {{text}}",
                "⭐",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.NewPostFromFavouritePackageUser), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFavouritePackageUser)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.NewPostFromFavouritePackageUser), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFavouritePackageUser)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.NewPostFromFavouritePackageUser), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFavouritePackageUser)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.NewPostFromFavouritePackageUser), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFavouritePackageUser)),
                Arrays.asList(createTemplateVariable("username", "User Name", "User", NotificationType.NewPostFromFavouritePackageUser), createTemplateVariable("text", "Post Title", "Post", NotificationType.NewPostFromFavouritePackageUser)));

        createNotificationTemplate(NotificationType.NewStoryFromFavouritePackageUser,
                "New Story from Favourite Package User",
                "{{username}} shared a new story in your favourite package",
                "📖",
                List.of(NotificationChannelType.Push),
                List.of(createTemplateVariable("username", "User Name", "User", NotificationType.NewStoryFromFavouritePackageUser)),
                List.of(createTemplateVariable("username", "User Name", "User", NotificationType.NewStoryFromFavouritePackageUser)),
                List.of(createTemplateVariable("username", "User Name", "User", NotificationType.NewStoryFromFavouritePackageUser)),
                List.of(createTemplateVariable("username", "User Name", "User", NotificationType.NewStoryFromFavouritePackageUser)),
                List.of(createTemplateVariable("username", "User Name", "User", NotificationType.NewStoryFromFavouritePackageUser)));

        createNotificationTemplate(NotificationType.ChatMessage,
                "{{username}}",
                "{{messageText}}",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)));


        createNotificationTemplate(NotificationType.ChatMessageVideo,
                "{{username}}",
                "sent you a video",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)));

        createNotificationTemplate(NotificationType.ChatMessageAudio,
                "{{username}}",
                "sent you an audio",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)));


        createNotificationTemplate(NotificationType.ChatMessageFile,
                "{{username}}",
                "sent you a file",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)));


        createNotificationTemplate(NotificationType.ChatMessagePoll,
                "{{username}}",
                "created a poll",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)));

        createNotificationTemplate(NotificationType.ChatMessagePoll,
                "{{username}}",
                "sent an image",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.ChatMessage), createTemplateVariable("messageText", "Message Text", "chatMessage", NotificationType.ChatMessage)));


        createNotificationTemplate(NotificationType.GroupChatMessage,
                "{{name}}",
                "{{username}}: {{text}}",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));

        createNotificationTemplate(NotificationType.GroupChatMessageVideo,
                "{{name}}",
                "{{username}} sent a video",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));


        createNotificationTemplate(NotificationType.GroupChatMessageAudio,
                "{{name}}",
                "{{username}} sent an audio",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));

        createNotificationTemplate(NotificationType.GroupChatMessageFile,
                "{{name}}",
                "{{username}} sent a file",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));

        createNotificationTemplate(NotificationType.GroupChatMessagePoll,
                "{{name}}",
                "{{username}} created a poll",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));

        createNotificationTemplate(NotificationType.GroupChatMessageImage,
                "{{name}}",
                "{{username}} sent an image",
                "imgs/icon/2025/06/20/8978e397-99d9-46bc-902f-c545032ff76c.svg",
                List.of(NotificationChannelType.Push),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("username", "Sender Name", "User", NotificationType.GroupChatMessage), createTemplateVariable("name", "Group Name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));



        createNotificationTemplate(NotificationType.AcceptPackage,
                "Package Approved!",
                "Your package {{name}} was approved and ready to post",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                List.of(NotificationChannelType.Email),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));

        createNotificationTemplate(NotificationType.PostPackage,
                "Package Posted!",
                "Your package {{name}} posted successfully",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                List.of(NotificationChannelType.Email),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));


        createNotificationTemplate(NotificationType.RejectPackage,
                "Package Rejected!",
                "Your package {{name}} was rejected",
                "imgs/icon/2025/06/20/795d5f67-af93-41d3-a79a-f4b387fd2a3a.svg",
                List.of(NotificationChannelType.Email),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)),
                Arrays.asList(createTemplateVariable("name", "Package name", "SubPackage", NotificationType.GroupChatMessage), createTemplateVariable("text", "Message Text", "GroupChatMessage", NotificationType.GroupChatMessage)));


    }

    private NotificationTemplateVariable createTemplateVariable(String name, String label, String entity, NotificationType type) {
        // First, try to find an existing variable with the same name and entity
        Optional<NotificationTemplateVariable> existingVariable = notificationTemplateVariableRepository
                .findByNameAndEntity(name, entity);

        if (existingVariable.isPresent()) {
            // Return the existing variable if found
            return existingVariable.get();
        }

        // Create a new variable if not found
        NotificationTemplateVariable variable = new NotificationTemplateVariable();
        variable.setName(name);
        variable.setLabel(label);
        variable.setEntity(entity);
        variable.setType(type);

        // Save and return the new variable
        return notificationTemplateVariableRepository.save(variable);
    }

    private void createNotificationTemplate(NotificationType type, String subject, String body, String icon,
                                            List<NotificationChannelType> channelTypes,
                                            List<NotificationTemplateVariable> pushVariables,
                                            List<NotificationTemplateVariable> inAppVariables,
                                            List<NotificationTemplateVariable> emailVariables,
                                            List<NotificationTemplateVariable> smsVariables,
                                            List<NotificationTemplateVariable> whatsAppVariables) {
        NotificationTemplate template = new NotificationTemplate();
        template.setNotificationType(type);
        template.setChannelTypes(channelTypes);
        template.setStatus(true);

        // Create Push notification template if Push is in channelTypes
        if (channelTypes.contains(NotificationChannelType.Push)) {
            NotificationTemplatePush push = new NotificationTemplatePush();
            push.setSubject(subject);
            push.setBody(body);
            push.setIcon(icon);
            push.setStatus(true);
            push.setTemplateVariables(pushVariables);
            template.setPush(push);
        }

        // Create InApp notification template if InApp is in channelTypes
        if (channelTypes.contains(NotificationChannelType.InApp)) {
            NotificationTemplateInApp inApp = new NotificationTemplateInApp();
            inApp.setSubject(subject);
            inApp.setBody(body);
            inApp.setIcon(icon);
            inApp.setStatus(true);
            inApp.setTemplateVariables(inAppVariables);
            template.setInApp(inApp);
        }

        // Create Email notification template if Email is in channelTypes
        if (channelTypes.contains(NotificationChannelType.Email)) {
            NotificationTemplateEmail email = new NotificationTemplateEmail();
            email.setSubject(subject);
            email.setBody(body);
            email.setStatus(true);
            email.setTemplateVariables(emailVariables);
            template.setEmail(email);
        }

        // Create SMS notification template if SMS is in channelTypes
        if (channelTypes.contains(NotificationChannelType.Sms)) {
            NotificationTemplateSms sms = new NotificationTemplateSms();
            sms.setBody(body);
            sms.setStatus(true);
            sms.setTemplateVariables(smsVariables);
            template.setSms(sms);
        }

        // Create WhatsApp notification template if WhatsApp is in channelTypes
        if (channelTypes.contains(NotificationChannelType.WhatsApp)) {
            NotificationTemplateWhatsApp whatsApp = new NotificationTemplateWhatsApp();
            whatsApp.setBody(body);
            whatsApp.setStatus(true);
            whatsApp.setTemplateVariables(whatsAppVariables);
            template.setWhatsApp(whatsApp);
        }

        notificationTemplateRepository.save(template);
    }
}

