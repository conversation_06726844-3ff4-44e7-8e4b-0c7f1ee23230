package com.hb.crm.admin.services;

import com.hb.crm.admin.services.interfaces.QueryNormalizeService;
import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.SearchEnum;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

@Service
public class QueryNormalizeServiceImpl implements QueryNormalizeService {

    @Override
    public Aggregation getUsersList(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();



        // Project only the required fields
        operations.add(project("_id",
                "firstName", "lastName",
                "email", "mobile", "gender",
                "city", "country", "usertype","username",
                "follwerscount", "followingcount",
                "coverImage", "profileImage"));

        // Apply sort from the pageable
        if (criteria != null) {
            operations.add(match(criteria));
        }
        return Aggregation.newAggregation(operations);
    }
    @Override
    public Aggregation getPostFields(Criteria criteria , Pageable pageable , Sort sort , boolean count) {
        List<AggregationOperation> operations = new ArrayList<>();
        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");

        // Perform a left outer join with the 'package' collection
        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("Package.$id")
                .foreignField("_id")
                .as("packageLookup");
        operations.add(packageLookupOperation);
        operations.add(userLookupOperation);
        operations.add(Aggregation.project("_id", "text", "media", "latitude", "longtuid",
                        "place", "postType", "created", "update",
                        "PostedDate", "tags", "postStatus", "rejectionNote",
                        "note", "commentsCount", "reactsCount", "viewsCount")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$packageLookup._id").elementAt(0)).as("Package._id")
                .and(ArrayOperators.arrayOf("$packageLookup.name").elementAt(0)).as("Package.name")
        );
        return getGridAggregation(criteria, pageable, sort, count, operations);
    }

    @Override
    public Aggregation getPackage(Criteria criteria ,Pageable pageable ,boolean count){
        List<AggregationOperation> operations = new ArrayList<>();


        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("_package.$id")
                .foreignField("_id")
                .as("packageLookup");
        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("packageLookup.infulancer.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation moodLookupOperation = LookupOperation.newLookup()
                .from("mood")
                .localField("packageLookup.moods.$id")
                .foreignField("_id")
                .as("moodLookup");



        LookupOperation tagLookupOperation = LookupOperation.newLookup()
                .from("tag")
                .localField("packageLookup.tags.$id")
                .foreignField("_id")
                .as("tagLookup");
        operations.add(packageLookupOperation);

        operations.add(userLookupOperation);
        operations.add(moodLookupOperation);
        operations.add(tagLookupOperation);

        operations.add(project("_id","name","state","start","end","subscribeCount","capacity","packageStatus","packageType","totalPrice","numberOfRoom","creationDate","updateDate")

                .and(ArrayOperators.arrayOf("$packageLookup.name").elementAt(0)).as("_package.name")
                .and(ArrayOperators.arrayOf("$packageLookup.latitude").elementAt(0)).as("_package.latitude")
                .and(ArrayOperators.arrayOf("$packageLookup.longtuid").elementAt(0)).as("_package.longtuid")
                .and(ArrayOperators.arrayOf("$packageLookup.latitudeTo").elementAt(0)).as("_package.latitudeTo")
                .and(ArrayOperators.arrayOf("$packageLookup.longtuidTo").elementAt(0)).as("_package.longtuidTo")
                .and(ArrayOperators.arrayOf("$packageLookup.Place").elementAt(0)).as("_package.Place")
                .and(ArrayOperators.arrayOf("$packageLookup.ToPlace").elementAt(0)).as("_package.ToPlace")
                .and(ArrayOperators.arrayOf("$packageLookup.availableForFollowMe").elementAt(0)).as("_package.availableForFollowMe")
                .and(ArrayOperators.arrayOf("$packageLookup.availableFrom").elementAt(0)).as("_package.availableFrom")
                .and(ArrayOperators.arrayOf("$packageLookup.followMeDiscount").elementAt(0)).as("_package.followMeDiscount")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_package.infulancer._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_package.infulancer.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_package.infulancer.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("_package.infulancer.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("_package.infulancer.username")
                .and(ArrayOperators.arrayOf("$packageLookup.description").elementAt(0)).as("_package.description")

        );






        if(pageable !=null){
            return getGridAggregation(criteria,pageable,pageable.getSort(),count,operations);

        }else {
            return getGridAggregation(criteria,null, null,count,operations);

        }

    }

    @Override
    public Aggregation getUsersWithRecentStoriesSplash(Criteria criteria, String currentUserId,int limitPerUser, Pageable pageable, boolean skipUpdateFilter) {
        List<AggregationOperation> operations = new ArrayList<>();

        LocalDateTime yesterday = LocalDateTime.now().minusHours(24);

        if (criteria != null)
            operations.add(match(criteria));
        if (currentUserId != null && !currentUserId.isEmpty()) {
            operations.add(context -> new Document("$addFields",
                    new Document("isCurrentUser",
                            new Document("$cond", Arrays.asList(
                                    new Document("$eq", Arrays.asList("$_id", new ObjectId(currentUserId))),
                                    1,
                                    0
                            ))
                    )
            ));
        }



        // Lookup posts per user
        operations.add(LookupOperation.newLookup()
                .from("post")
                .localField("_id")
                .foreignField("user.$id")
                .as("userPosts"));

        // ✅ Lookup packages based on Package ref inside userPosts
        operations.add(LookupOperation.newLookup()
                .from("package")
                .localField("userPosts.Package.$id")
                .foreignField("_id")
                .as("Package"));

        operations.add(LookupOperation.newLookup()
                .from("media")
                .localField("userPosts.media.media.$id")
                .foreignField("_id")
                .as("Media"));

        // Filter only recent stories
        // Filter only recent stories
        operations.add(addFields()
                .addFieldWithValue("recentStories", new Document("$filter", new Document("input", "$userPosts")
                        .append("as", "post")
                        .append("cond", new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$$post.postType", "Story")),
                                new Document("$eq", Arrays.asList("$$post.postStatus", "posted")),
                                new Document("$cond", Arrays.asList(
                                        skipUpdateFilter,
                                        true,
                                        new Document("$gte", Arrays.asList("$$post.update", yesterday))
                                ))
                        ))))
                ).build());

        if(limitPerUser >0){
            // Limit recent stories to 10 per user
            operations.add(addFields()
                    .addFieldWithValue("recentStories", new Document("$slice", Arrays.asList("$recentStories", limitPerUser)))
                    .build());

        }

        // Add the latest update date from stories
        operations.add(context -> new Document("$addFields",
                new Document("latestUpdate",
                        new Document("$reduce", new Document("input", "$recentStories")
                                .append("initialValue", null)
                                .append("in", new Document("$cond", Arrays.asList(
                                        new Document("$gt", Arrays.asList("$$this.update", "$$value")),
                                        "$$this.update",
                                        "$$value"
                                )))
                        )
                )
        ));

        // Match only users who have recent stories
        operations.add(match(Criteria.where("recentStories").not().size(0)));

        // ✅ Project stories and join correct package using $filter
        ProjectionOperation projectOp = project("_id", "firstName", "lastName", "coverImage", "profileImage", "latestUpdate", "gender", "username", "usertype")
                .and(new AggregationExpression() {
                    @Override
                    public @NotNull Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$recentStories")
                                .append("as", "story")
                                .append("in", new Document("$let", new Document()
                                        .append("vars", new Document()
                                                .append("package", new Document("$arrayElemAt", Arrays.asList(
                                                        new Document("$filter", new Document()
                                                                .append("input", "$Package")
                                                                .append("as", "pkg")
                                                                .append("cond", new Document("$eq", Arrays.asList("$$pkg._id", "$$story.Package.$id")))
                                                        ),
                                                        0
                                                )))
                                                .append("media", new Document("$arrayElemAt", Arrays.asList(
                                                        new Document("$filter", new Document()
                                                                .append("input", "$Media")
                                                                .append("as", "med")
                                                                .append("cond", new Document("$eq", Arrays.asList(
                                                                        "$$med._id",
                                                                        new Document("$arrayElemAt", Arrays.asList("$$story.media.media.$id", 0))
                                                                )))
                                                        ),
                                                        0
                                                )))
                                        )
                                        .append("in", new Document()
                                                .append("_id", "$$story._id")
                                                .append("text", "$$story.text")
                                                .append("created", "$$story.created")
                                                .append("mediaUrl", new Document("$arrayElemAt", Arrays.asList("$$story.media.url", 0)))
                                                .append("mediaType", new Document("$arrayElemAt", Arrays.asList("$$story.media.type", 0)))
                                                .append("thumbnailUrl", "$$media.thumbnailCaptureUrl")
                                                .append("videoDuration", "$$media.videoSize")
                                                .append("packageId", "$$package._id")
                                                .append("packageName", "$$package.name")
                                                .append("packageSlug", "$$package.slug")
                                        )
                                ))
                        );
                    }
                }).as("stories");

        operations.add(projectOp);
        if (currentUserId != null && !currentUserId.isEmpty()) {
            operations.add(Aggregation.sort(Sort.by(
                    Sort.Order.desc("isCurrentUser"),
                    Sort.Order.desc("latestUpdate")
            )));
        }else {
            operations.add(Aggregation.sort(Sort.by(
                    Sort.Order.desc("latestUpdate")
            )));
        }
        return getGridAggregation(null, pageable, null, operations);
    }
    @Override
    public Aggregation getAlphaPackageList(Criteria criteria ){
        List<AggregationOperation> operations = new ArrayList<>();


        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("_package.$id")
                .foreignField("_id")
                .as("packageLookup");


        Criteria newcriteria = new Criteria();
        newcriteria.where("packageType").is(PackageType.TravelWithMe);

        operations.add(Aggregation.match(newcriteria));



        operations.add(packageLookupOperation);



        operations.add(project("_id","creationDate","updateDate","packageStatus","packageType","name")

                .and(ArrayOperators.arrayOf("$packageLookup.name").elementAt(0)).as("_package.name")
                .and(ArrayOperators.arrayOf("$packageLookup._id").elementAt(0)).as("_package._id")



        );




            return getGridAggregation(criteria,null, null,false,operations);

    }

    @Override
    public    Aggregation getSubscribePackage(Criteria criteria  ) {
        List<AggregationOperation> operations = new ArrayList<>();



        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");



        // Perform a left outer join with the 'package' collection

        LookupOperation  subpackageLookup = LookupOperation.newLookup()
                .from("subPackage")
                .localField("_id._package.$id")
                .foreignField("_id")
                .as("subPackageLookup");

        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("subPackageLookup._package.$id")
                .foreignField("_id")
                .as("packageLookup");


        operations.add(userLookupOperation);
        operations.add(subpackageLookup);
        operations.add(packageLookupOperation);


        operations.add(project("total","expireDate","status","travelers")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("_id.user.username")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("_id.user.profileImage")
                .and(ArrayOperators.arrayOf("subPackageLookup._id").elementAt(0)).as("_id._package._id")
                .and(ArrayOperators.arrayOf("subPackageLookup.name").elementAt(0)).as("_id._package.name")
                .and(ArrayOperators.arrayOf("subPackageLookup.state").elementAt(0)).as("_id._package.state")
                .and(ArrayOperators.arrayOf("subPackageLookup.start").elementAt(0)).as("_id._package.start")
                .and(ArrayOperators.arrayOf("subPackageLookup.end").elementAt(0)).as("_id._package.end")
                .and(ArrayOperators.arrayOf("subPackageLookup.creationDate").elementAt(0)).as("_id._package.creationDate")
                .and(ArrayOperators.arrayOf("subPackageLookup.activities").elementAt(0)).as("_id._package.activities")
                .and(ArrayOperators.arrayOf("subPackageLookup.packageType").elementAt(0)).as("_id._package.packageType")
                .and(ArrayOperators.arrayOf("subPackageLookup.subscribeCount").elementAt(0)).as("_id._package.subscribeCount")
                .and(ArrayOperators.arrayOf("subPackageLookup.capacity").elementAt(0)).as("_id._package.capacity")
                .and(ArrayOperators.arrayOf("packageLookup.description").elementAt(0)).as("_id._package._package.description")
                .and(ArrayOperators.arrayOf("packageLookup._id").elementAt(0)).as("_id._package._package._id")
                .and(ArrayOperators.arrayOf("packageLookup.name").elementAt(0)).as("_id._package._package.name")
                .and(ArrayOperators.arrayOf("packageLookup.latitude").elementAt(0)).as("_id._package._package.latitude")
                .and(ArrayOperators.arrayOf("packageLookup.longtuid").elementAt(0)).as("_id._package._package.longtuid")
                .and(ArrayOperators.arrayOf("packageLookup.latitudeTo").elementAt(0)).as("_id._package._package.latitudeTo")
                .and(ArrayOperators.arrayOf("packageLookup.longtuidTo").elementAt(0)).as("_id._package._package.longtuidTo")
                .and(ArrayOperators.arrayOf("packageLookup.Place").elementAt(0)).as("_id._package._package.Place")
                .and(ArrayOperators.arrayOf("packageLookup.ToPlace").elementAt(0)).as("_id._package._package.ToPlace")
                .and(ArrayOperators.arrayOf("packageLookup.availableForFollowMe").elementAt(0)).as("_id._package._package.availableForFollowMe")
  
        );




        if (criteria != null) {
            operations.add(match(criteria));
        }


        return newAggregation(operations);
    }
    @Override
    public    Aggregation getSubscribePackage(Criteria criteria ,Pageable pageable ,  Sort sort) {
        List<AggregationOperation> operations = new ArrayList<>();
        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation subBackageLookup = LookupOperation.newLookup()
                .from("subPackage")
                .localField("_id._package.$id")
                .foreignField("_id")
                .as("subPackageLookup");
        operations.add(userLookupOperation);
        operations.add(subBackageLookup);
        // Add the traveler count using $size
        String addFieldsJson = "{ \"$addFields\": { \"travelerCount\": { \"$size\": { \"$ifNull\": [ \"$travelers\", [] ] } } } }";
        AggregationOperation addTravelerCount = context -> new Document("$addFields", Document.parse(addFieldsJson).get("$addFields"));

        operations.add(addTravelerCount);



        operations.add(project("total","expireDate","status","travelerCount")
                .and(ArrayOperators.arrayOf("userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("subPackageLookup._id").elementAt(0)).as("_id._package._id")
                .and(ArrayOperators.arrayOf("userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("userLookup.firstName").elementAt(0)).as("mainTravelerFirstName")
                .and(ArrayOperators.arrayOf("userLookup.lastName").elementAt(0)).as("mainTravelerLastName")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.mobile").elementAt(0)).as("_id.user.userInfo.mobile")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.email").elementAt(0)).as("_id.user.userInfo.email")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.mobile").elementAt(0)).as("mainTravelerPhoneNumber")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.email").elementAt(0)).as("mainTravelerEmail")
                .and(ArrayOperators.arrayOf("userLookup.guestEmail").elementAt(0)).as("gustEmail")
                .and(ArrayOperators.arrayOf("userLookup.profileImage").elementAt(0)).as("_id.user.profileImage")
        );

        return  getGridAggregation(criteria,pageable, sort,operations);
    }
    @Override
    public    Aggregation getSubscribePackageByID(Criteria criteria ) {
        List<AggregationOperation> operations = new ArrayList<>();
        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation subBackageLookup = LookupOperation.newLookup()
                .from("subPackage")
                .localField("_id._package.$id")
                .foreignField("_id")
                .as("subPackageLookup");
        operations.add(userLookupOperation);
        operations.add(subBackageLookup);
        // Add the traveler count using $size




        operations.add(project("total","expireDate","status","travelers"
                ,"RoomReservations","hotels","flights","confirmPriceRefraceId","flgihtCabins","numberOfRooms","adultCount","childCount",
                "documents","bockingDetailsUrl","departureFlight","destinationFlight","preferences","otherHotelPreferences","otherFlightPreferences","updateDate")
                .and(ArrayOperators.arrayOf("userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("subPackageLookup._id").elementAt(0)).as("_id._package._id")
                .and(ArrayOperators.arrayOf("userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("userLookup.firstName").elementAt(0)).as("mainTravelerFirstName")
                .and(ArrayOperators.arrayOf("userLookup.lastName").elementAt(0)).as("mainTravelerLastName")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.mobile").elementAt(0)).as("_id.user.userInfo.mobile")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.email").elementAt(0)).as("_id.user.userInfo.email")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.mobile").elementAt(0)).as("mainTravelerPhoneNumber")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.email").elementAt(0)).as("mainTravelerEmail")
                .and(ArrayOperators.arrayOf("userLookup.profileImage").elementAt(0)).as("_id.user.profileImage")
        );
        // Add the traveler count using $size
        if (criteria != null) {
            operations.add(match(criteria));
        }

        return  newAggregation(operations);
    }
    @Override
    public Aggregation getInfluence(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(Aggregation.project("_id",
                "firstName", "lastName",
                "email", "mobile", "gender",
                "city", "country",
                "like", "usertype",
                "username"
                ,"follwerscount", "followingcount",
                 "coverImage", "profileImage"));
        if (criteria != null) {
            operations.add(Aggregation.match(criteria));
        }
        return Aggregation.newAggregation(operations);

    }

    @Override
    public Aggregation getUserFileds(Criteria criteria, Pageable pageable, boolean count) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Apply the filtering criteria
        if (criteria != null) {
            operations.add(Aggregation.match(criteria));
        }

        if (count) {
            // Add a $group stage to count the total number of matching documents
            operations.add(Aggregation.group().count().as("count"));
        } else {
            // Project only the required fields
            ProjectionOperation project = Aggregation.project()
                    .and("_id").as("id")
                    .and("creationDate").as("creationDate")
                    .and("updatedDate").as("updatedDate")
                    .and("about").as("about")
                    .and("username").as("username")
                    .and("password").as("password")
                    .and("firstName").as("firstName")
                    .and("lastName").as("lastName")
                    .and("userInfo.email").as("email")  // Map nested email to top-level
                    .and("userInfo.mobile").as("mobile")
                    .and("gender").as("gender")
                    .and("like").as("like")
                    .and("code").as("code")
                    .and("role").as("role")
                    .and("userInfo").as("userInfo")
                    .and("city").as("city")
                    .and("country").as("country")
                    .and("usertype").as("usertype")
                    .and("EmailActivated").as("EmailActivated")
                    .and("medias").as("medias")
                    .and("coverImage").as("coverImage")
                    .and("profileImage").as("profileImage");

            operations.add(project);

            // Add pagination operations
            if (pageable != null) {

                operations.add(Aggregation.skip(pageable.getOffset()));
             //   operations.add(Aggregation.skip((long) pageable.getPageNumber() * pageable.getPageSize()));
                operations.add(Aggregation.limit(pageable.getPageSize()));
            }
        }

        // Return the aggregation pipeline
        return Aggregation.newAggregation(operations);
    }


    @Override
    public Aggregation getPostReactionFields(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");

        // Perform a left outer join with the 'package' collection
        LookupOperation postLookupOperation = LookupOperation.newLookup()
                .from("post")
                .localField("post.$id")
                .foreignField("_id")
                .as("postLookup");
        operations.add(postLookupOperation);
        operations.add(userLookupOperation);

        operations.add(Aggregation.project("_id")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$postLookup._id").elementAt(0)).as("post._id")

        );


        if (criteria != null) {
            operations.add(Aggregation.match(criteria));
        }



        return Aggregation.newAggregation(operations);
    }



    @Override
    public  Aggregation getReplayReacts(Criteria criteria  ){
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation replyLookupOperation = LookupOperation.newLookup()
                .from("reply")
                .localField("reply.$id")
                .foreignField("_id")
                .as("replyLookup");
        operations.add(userLookupOperation);
        operations.add(replyLookupOperation);

        if (criteria != null) {
            operations.add(match(criteria));
        }
        operations.add(project("_id","reactionType")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("replyLookup._id").elementAt(0)).as("reply._id"));

        return newAggregation(operations);
    }
    @Override
    public    Aggregation getCommentReacts(Criteria criteria  ){
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation commentLookupOperation = LookupOperation.newLookup()
                .from("comment")
                .localField("comment.$id")
                .foreignField("_id")
                .as("commentLookup");
        operations.add(userLookupOperation);
        operations.add(commentLookupOperation);


        operations.add(project("_id","reactionType")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("commentLookup._id").elementAt(0)).as("comment._id"));

        if (criteria != null) {
            operations.add(match(criteria));
        }
        return newAggregation(operations);
    }
    @Override
    public    Aggregation getPackageReacts(Criteria criteria){
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation commentLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("_id._package.$id")
                .foreignField("_id")
                .as("packageLookup");
        operations.add(userLookupOperation);
        operations.add(commentLookupOperation);


        operations.add(project("loveIt")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("_id.user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("_id.user.username")
                .and(ArrayOperators.arrayOf("packageLookup._id").elementAt(0)).as("_id._package._id"));

        if (criteria != null) {
            operations.add(match(criteria));
        }
        return newAggregation(operations);
    }
     @Override
     public Aggregation getPostComments(Criteria criteria, Pageable pageable) {
         List<AggregationOperation> operations = new ArrayList<>();

         LookupOperation userLookupOperation = LookupOperation.newLookup()
                 .from("user")
                 .localField("user.$id")
                 .foreignField("_id")
                 .as("userLookup");

         LookupOperation postLookupOperation = LookupOperation.newLookup()
                 .from("post")
                 .localField("post.$id")
                 .foreignField("_id")
                 .as("postLookup");

         LookupOperation mentionsLookupOperation = LookupOperation.newLookup()
                 .from("user")
                 .localField("mentions.$id")
                 .foreignField("_id")
                 .as("mentionLookup");

         operations.add(userLookupOperation);
         operations.add(postLookupOperation);
         operations.add(mentionsLookupOperation);
         operations.add(project("_id", "comment", "createdDate", "numberOfReactions", "numberOfReplyes")
                 .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                 .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                 .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                 .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                 .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                 .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                 .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                 .and(ArrayOperators.arrayOf("$postLookup._id").elementAt(0)).as("post._id")
                 .and(new AggregationExpression() {
                     @Override
                     public Document toDocument(AggregationOperationContext context) {
                         return new Document("$map", new Document()
                                 .append("input", "$mentionLookup")
                                 .append("as", "user")
                                 .append("in", new Document()
                                         .append("_id", "$$user._id")
                                         .append("firstName", "$$user.firstName")
                                         .append("lastName", "$$user.lastName")
                                         .append("username", "$$user.username")
                                         .append("usertype", "$$user.usertype")
                                         .append("profileImage", "$$user.profileImage")
                                 )
                         );
                     }
                 }).as("mentions")
         );
         if (criteria != null) {
             operations.add(match(criteria));
         }

         return getGridAggregation(criteria, pageable, null, operations);
     }
    @NotNull
    private Aggregation getGridAggregation(Criteria criteria,Pageable pageable,Sort sort, boolean count, List<AggregationOperation> operations) {
        if (criteria != null) {
            operations.add(Aggregation.match(criteria));
        }
        if(count){
            operations.add(  Aggregation.group().count().as("count"));
        }else {
            if(sort!=null)
                operations.add( Aggregation.sort(sort));
            if(pageable !=null){
                operations.add(Aggregation.skip(pageable.getOffset()));
                operations.add(Aggregation.limit(pageable.getPageSize()));
            }

        }

        return Aggregation.newAggregation(operations);
    }



    @Override
    public Aggregation getCountryCityAreaFields(Criteria criteria, Pageable pageable, Sort sort, boolean count) {
        List<AggregationOperation> operations = new ArrayList<>();

        if (criteria != null) {
            operations.add(match(criteria));
        }

        LookupOperation countryLookupOperation = LookupOperation.newLookup()
                .from("city")
                .localField("_id")
                .foreignField("country.$id")
                .as("cities");
        operations.add(countryLookupOperation);

        operations.add(
                Aggregation.project("_id","name", "cities"  ) // Include countryName and cities

        );

        return getGridAggregation(criteria, pageable, sort, count, operations);
    }
    @Override
    public Aggregation getCitiesAreaFields(Criteria criteria, Pageable pageable, Sort sort) {
        List<AggregationOperation> operations = new ArrayList<>();

        if (criteria != null) {
            operations.add(match(criteria));
        }

        // Step 2: Perform lookup to get areas for each city
        LookupOperation countryLookupOperation = LookupOperation.newLookup()
                .from("area")
                .localField("_id")
                .foreignField("city.$id")
                .as("areas");
        operations.add(countryLookupOperation);

        // Step 3: Project fields and extract countryId from the DBRef
        // Step 3: Project fields and extract the country ID from the DBRef
        operations.add(
                Aggregation.project("_id","name","areas") // Include countryName and cities

        );



        return getGridAggregation(criteria, pageable, sort, operations);
    }



    public Aggregation getCitiesAirportsFields(Criteria criteria, Pageable pageable, Sort sort, boolean count) {
        List<AggregationOperation> operations = new ArrayList<>();

        // 1. Extract the country id from the DBRef in the "country" field
        operations.add(
                Aggregation.addFields()
                        .addField("countryId")
                        .withValue("$country.$id")
                        .build()
        );

        // 2. Lookup the country document using the extracted countryId.
        LookupOperation countryLookup = LookupOperation.newLookup()
                .from("country")
                .localField("countryId")
                .foreignField("_id")
                .as("country");
        operations.add(countryLookup);

        // 3. Unwind the country array to flatten it.
        operations.add(Aggregation.unwind("country", true)); // true preserves documents with no matching country

        // 4. Now apply the filtering criteria.
        if (criteria != null) {
            operations.add(Aggregation.match(criteria));
        }

        // 5. Project the fields required in your DTO.
        operations.add(Aggregation.project()
                .and("_id").as("id")
                .and("name").as("name")
                .and("airportList").as("airportList")
                // Project the country's id and name into the nested country object.
                .and("countryId").as("country.id")
                .and("country.name").as("country.name")
        );

        // 6. Delegate pagination/sorting to your helper (if applicable)
        return getGridAggregation(criteria, pageable, sort, count, operations);
    }

    @Override
    public Aggregation getSearch(String query, SearchEnum classmate, Pageable pageable, Criteria criteria) {

        PackageType packageTypeFilter= null;

        if(classmate.equals(SearchEnum.Package))
            packageTypeFilter=PackageType.TravelWithMe;

        if(classmate.equals(SearchEnum.followPackage))
            packageTypeFilter=PackageType.FollowMe;

        List<AggregationOperation> operations = new ArrayList<>();
        String classFilter = extractClass(classmate);

        // Check if both query and classFilter are null or empty
        if ((query == null || query.isEmpty()) && (classFilter == null || classFilter.isEmpty())) {
            // If both are not provided, fetch all documents
            return getGridAggregation(criteria, pageable, pageable.getSort(), operations);
        }


        // Create the $search stage
        AggregationOperation searchStage = new AggregationOperation() {
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {

                List<Document> mustClauses = new ArrayList<>();

                // Add _class filter if provided
                if (classFilter != null && !classFilter.isEmpty()) {
                    mustClauses.add(new Document()
                            .append("equals", new Document()
                                    .append("value", classFilter)
                                    .append("path", "_class")));
                }

                // Add text search if query is provided
                if (query != null && !query.isEmpty()) {
                    List<Document> shouldClauses = new ArrayList<>();

                    // Fuzzy search on specific fields
                    shouldClauses.add(new Document("text", new Document()
                            .append("query", query)
                            .append("path", Arrays.asList("text","Package.name","Package.slug", "name",
                                    "infulancer.firstName","infulancer.lastName",
                                    "user.firstName","user.lastName"

                            ))
                            .append("fuzzy", new Document()
                                    .append("maxEdits", 2)
                                    .append("prefixLength", 0)
                                    .append("maxExpansions", 50)
                            )
                    ));

                    // Wildcard search across all fields
                    shouldClauses.add(new Document("text", new Document()
                            .append("query", query)
                            .append("path", new Document("wildcard", "*"))
                    ));

                    // Add compound.should clause with minimumShouldMatch
                    mustClauses.add(new Document("compound", new Document()
                            .append("should", shouldClauses)
                            .append("minimumShouldMatch", 1)
                    ));
                }

                return new Document("$search", new Document()
                        .append("index", "default")
                        .append("compound", new Document()
                                .append("must", mustClauses)));
            }
        };

        operations.add(searchStage);

        // Add additional filters using $match stage
        if ("com.hb.crm.core.searchBeans.searchPackage".equals(classFilter)) {
            if (packageTypeFilter != null) {
                if (packageTypeFilter == PackageType.TravelWithMe) {
                    operations.add(
                            match(
                                    new Criteria().orOperator(
                                            Criteria.where("state").is(0),
                                            Criteria.where("state").is(3)
                                    )
                            )
                    );
                }
                operations.add(match(Criteria.where("packageType").is(packageTypeFilter)));
            }
            operations.add(match(Criteria.where("packageStatus").is(PackageStatus.posted)));
        }

        return getGridAggregation(criteria, pageable, pageable.getSort(), operations);
    }

    @Override
    public Aggregation getAirportFields(Criteria criteria, Pageable pageable, boolean count) {
        List<AggregationOperation> operations = new ArrayList<>();

        // 1. Apply filtering criteria (if any)
        if (criteria != null) {
            operations.add(Aggregation.match(criteria));
        }

        if (count) {
            // 2a. If we're only counting, add a group stage that counts the documents
            operations.add(Aggregation.group().count().as("count"));
        } else {
            // 2b. Otherwise, project only the required airport fields
            operations.add(Aggregation.project(
                    "_id", "name", "code", "city", "country",
                    "cityId", "countryId", "latitude", "longitude"
            ));

            // Optional: If you need to sort the results, add a sort stage.
            // operations.add(Aggregation.sort(Sort.by(Sort.Direction.DESC, "name")));

            // 3. Apply pagination if pageable is provided
            if (pageable != null) {
                operations.add(Aggregation.skip(pageable.getOffset()));
                operations.add(Aggregation.limit(pageable.getPageSize()));
            }
        }

        // Return the constructed aggregation pipeline
        return Aggregation.newAggregation(operations);
    }

    @Override
        public Aggregation getAreaFields(Criteria criteria, Pageable pageable, boolean count) {
            List<AggregationOperation> operations = new ArrayList<>();

            // Apply filtering criteria
            if (criteria != null) {
                operations.add(Aggregation.match(criteria));
            }

            if (count) {
                // Count the total number of documents
                operations.add(Aggregation.group().count().as("count"));
            } else {
                // Project only required fields
                operations.add(Aggregation.project(
                        "_id", "name", "latitude", "longitude", "country", "countryId", "city"
                ));

                // Apply pagination
                if (pageable != null) {
                    operations.add(Aggregation.skip(pageable.getOffset()));
                    operations.add(Aggregation.limit(pageable.getPageSize()));
                }
            }

            return Aggregation.newAggregation(operations);
        }



        @Override
        public Aggregation searchActivityContent(Criteria criteria, Pageable pageable){
                List<AggregationOperation> operations = new ArrayList<>();

                Sort sort = null;
                if(pageable !=null){
                    sort= pageable.getSort();
                }
                 if(criteria!=null){
                    operations.add(match(criteria));
                }
            operations.add(
                    project("id", "name")
                            .and(new AggregationExpression() {
                                @Override
                                public Document toDocument(AggregationOperationContext context) {
                                    return new Document("$arrayElemAt", Arrays.asList(
                                            new Document("$map", new Document("input", new Document("$filter",
                                                    new Document("input", "$medias")
                                                            .append("as", "media")
                                                            .append("cond", new Document("$eq", Arrays.asList("$$media.mainImage", true)))
                                            ))
                                                    .append("as", "media")
                                                    .append("in", new Document("type", "$$media.type")
                                                            .append("caption", "$$media.caption")
                                                            .append("asset", "$$media.asset")
                                                            .append("url", "$$media.url")
                                                            .append("mainImage", "$$media.mainImage"))
                                            ), 0)
                                    );
                                }
                            }).as("media")
            );


            return getGridAggregation(null, pageable, sort, operations);

            }

    private String extractClass( SearchEnum classFilter){
        if(classFilter.equals(SearchEnum.Package))
            return "com.hb.crm.core.searchBeans.searchPackage";
        else if(classFilter.equals(SearchEnum.post))
            return "com.hb.crm.core.searchBeans.searchPost";
        else if (classFilter.equals(SearchEnum.story))
            return "com.hb.crm.core.searchBeans.searchStory";
        else
            return null;
    }

    @NotNull
    private Aggregation getGridAggregation(Criteria criteria, Pageable pageable, Sort sort, List<AggregationOperation> operations) {
        if (criteria != null) {
            operations.add(match(criteria));
        }


        if (sort != null)
            operations.add(sort(sort));
        if (pageable != null) {

            operations.addAll(generateSkipStage(pageable.getOffset(), pageable.getPageSize()));
        }


        return newAggregation(operations);
    }
    private List<AggregationOperation> generateSkipStage(long skip, long limit) {
        List<AggregationOperation> operations = new ArrayList<>();
        // Create the $skip stage if skip is greater than 0
        List<Document> skipStage = new ArrayList<>();
        if (skip > 0) {
            skipStage.add(new Document("$skip", skip));
        }

        // Create the $limit stage
        Document limitStage = new Document("$limit", limit);

        // Combine $skip and $limit stages
        List<Document> skipLimitStages = new ArrayList<>(skipStage);
        skipLimitStages.add(limitStage);

        // Create the $facet stage for Data and TotalCount as subPage Entity
        AggregationOperation facetStage = new AggregationOperation() {
            @Override
            public Document toDocument(AggregationOperationContext context) {
                return new Document("$facet", new Document()
                        .append("items", skipLimitStages)
                        .append("TotalCount", List.of(new Document("$count", "TotalCount"))));
            }
        };
        AggregationOperation projectStage = new AggregationOperation() {
            @Override
            public Document toDocument(AggregationOperationContext context) {
                return new Document("$project", new Document()
                        .append("items", 1)
                        .append("totalNoOfItems", new Document("$arrayElemAt", List.of("$TotalCount.TotalCount", 0))));
            }
        };
        operations.add(facetStage);
        operations.add(projectStage);
        // Create the $project stage to extract Data and TotalCount we have to project it since total count is arrayElement

        // Combine all stages

        return operations;


    }

}
