package com.hb.crm.client.dto.PackageDtos;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hb.crm.client.dto.MediaWrapperDto;
import com.hb.crm.client.dto.keyandvalue;
import com.hb.crm.client.dto.mood.MoodDto;
import com.hb.crm.client.dto.users.UserInfoDto;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.State;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


public class PackageDto {
    @Id
    private String id;
    private String link;
    private PackageType packageType = PackageType.TravelWithMe;
    private String name;
    private String description;
    private String theme;
    private Object details;
    private List<MediaWrapperDto> medias;

    @Getter
    @Setter
    private String slug;

    @JsonIgnore
    private AlphaPackage _package;
    private LocalDateTime start;
    private LocalDateTime end;
    @Setter
    private UserInfoDto infulancer;
    @Setter
    @Getter
    private int capacity;
    private int subscribeCount;


    @Setter
    private List<Tag> tags;

    private boolean favouritebyme;
    private boolean NotficationEnabledByMe;
    private boolean loveIt;
    private int numberOfRoom;
    private List<Rate> rates;
    private double AvgRate;
    private Rate myRate;
    private int state = 0;


    private List<MoodDto> moods;
    private List<ActivityDto> activities;
    private City city;
    private Country country;
    private BigDecimal totalPrice;
    private LocalDateTime creationDate;
    private String rejectionNote = "";
    private Airport fromAirport;
    private Airport toAirport;
    private int availabileSeat;
    private MediaWrapperDto brochure;
    private List<PackageCountry> packagePlaces;
    private boolean fromAirportInside;
    private boolean toAirportInside;
    private boolean availableForFollowMe;
    private LocalDateTime availableFrom;
    private long duration;
    private LocalDateTime UpdateDate;
    private boolean privateDate = false;
    private boolean exceedSeats;

    public List<PackageCountry> getPackagePlaces() {
        return packagePlaces;
    }

    public void setPackagePlaces(List<PackageCountry> packagePlaces) {
        this.packagePlaces = packagePlaces;
    }

    public LocalDateTime getUpdateDate() {
        return UpdateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        UpdateDate = updateDate;
    }

    public long getDuration() {

        if (this.start != null && this.end != null)
            return ChronoUnit.DAYS.between(this.start, this.end) + 1;
        return 0;

    }

    public boolean isPrivateDate() {
        return privateDate;
    }

    public void setPrivateDate(boolean privateDate) {
        this.privateDate = privateDate;
    }

    public boolean isAvailableForFollowMe() {
        return _package.isAvailableForFollowMe();
    }

    public void setAvailableForFollowMe(boolean availableForFollowMe) {
        this.availableForFollowMe = availableForFollowMe;
    }

    public int getNumberOfRoom() {
        return numberOfRoom;
    }

    public void setNumberOfRoom(int numberOfRoom) {
        this.numberOfRoom = numberOfRoom;
    }

    public LocalDateTime getAvailableFrom() {
        return _package.getAvailableFrom();
    }


    public void setAvailableFrom(LocalDateTime availableFrom) {
        this.availableFrom = availableFrom;
    }

    public MediaWrapperDto getMainImage() {
        return _package.getMainImage();
    }

    public void setMainImage(MediaWrapperDto mainImage) {
        this._package.setMainImage(mainImage);
    }

    public MediaWrapperDto getBrochure() {
        return _package.getBrochure();
    }

    public void setBrochure(MediaWrapperDto brochure) {
        this._package.setBrochure(brochure);
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public City getCity() {
        return city;
    }

    public void setCity(City city) {
        this.city = city;
    }

    public Country getCountry() {
        return country;
    }

    public void setCountry(Country country) {
        this.country = country;
    }

    public List<ActivityDto> getActivities() {
        return activities;
    }

    public void setActivities(List<ActivityDto> activities) {
        this.activities = activities;
    }

    public State getState() {
        return State.fromValue(this.state);
    }

    public void setState(State state) {
        this.state = state.getValue();
    }

    public boolean isFavouritebyme() {
        return favouritebyme;
    }

    public void setFavouritebyme(boolean favouritebyme) {
        this.favouritebyme = favouritebyme;
    }

    public List<Tag> getTags() {
        return _package.getTags();
    }

    public UserInfoDto getInfulancer() {
        return _package.getInfulancer();
    }

    public int getSubscribeCount() {
        return subscribeCount;
    }

    public void setSubscribeCount(int subscribeCount) {
        this.subscribeCount = subscribeCount;
    }

    public LocalDateTime getStart() {
        return start;
    }

    public void setStart(LocalDateTime start) {
        this.start = start;
    }

    public LocalDateTime getEnd() {
        return end;
    }

    public void setEnd(LocalDateTime end) {
        this.end = end;
    }

    public List<MediaWrapperDto> getMedias() {
        return _package.getMedias();
    }

    public void setMedias(List<MediaWrapperDto> medias) {
        _package.setMedias(medias);
    }

    public List<MoodDto> getMoods() {
        return _package.getMoods();
    }

    public void setMoods(List<MoodDto> moods) {
        _package.setMoods(moods);
    }

    public AlphaPackage get_package() {
        return _package;
    }

    public void set_package(AlphaPackage _package) {
        this._package = _package;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public PackageType getPackageType() {
        return packageType;
    }

    public void setPackageType(PackageType packageType) {
        this.packageType = packageType;
    }

    public String getName() {
        return _package != null ? _package.getName() : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return _package.getDescription();
    }

    public void setDescription(String description) {
        _package.setDescription(description);
    }

    public Object getDetails() {
        HashMap<String, String> map = (HashMap<String, String>) this.details;
        if (map == null) {
            return this.details;
        }
        return getingvalues(map);
    }

    public void setDetails(Object details) {
        this.details = details;
    }


    public String getRejectionNote() {
        return rejectionNote;
    }

    public void setRejectionNote(String rejectionNote) {
        this.rejectionNote = rejectionNote;
    }


    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }


    public Airport getFromAirport() {
        return this._package.getFromAirport();
    }

    public Airport getToAirport() {
        return this._package.getToAirport();
    }

    public int getAvailabileSeat() {
        return capacity - subscribeCount;
    }


    public boolean isNotficationEnabledByMe() {
        return NotficationEnabledByMe;
    }

    public void setNotficationEnabledByMe(boolean notficationEnabledByMe) {
        NotficationEnabledByMe = notficationEnabledByMe;
    }

    public boolean isLoveIt() {
        return loveIt;
    }

    public void setLoveIt(boolean loveIt) {
        this.loveIt = loveIt;
    }

    private List<keyandvalue> getingvalues(HashMap<String, String> map) {
        List<keyandvalue> values = new ArrayList<keyandvalue>();
        for (HashMap.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            values.add(new keyandvalue(key, value));
        }
        return values;
    }

    public List<Rate> getRates() {
        return this._package.getRates();
    }


    public double getAvgRate() {
        return this._package.getAvgRate();
    }

    public Rate getMyRate() {
        return myRate;
    }

    public void setMyRate(Rate myRate) {
        this.myRate = myRate;
    }

    public boolean isToAirportInside() {
        return this._package.isToAirportInside();

    }

    public void setToAirportInside(boolean toAirportInside) {
        this._package.setToAirportInside(toAirportInside);
    }

    public boolean isFromAirportInside() {
        return this._package.isFromAirportInside();
    }

    public void setFromAirportInside(boolean fromAirportInside) {
        this._package.setFromAirportInside(fromAirportInside);
    }

    public boolean isExceedSeats() {
        return subscribeCount > capacity;
    }


}
