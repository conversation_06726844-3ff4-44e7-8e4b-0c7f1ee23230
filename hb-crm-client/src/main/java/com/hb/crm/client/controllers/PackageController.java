package com.hb.crm.client.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.hb.crm.client.dto.*;
import com.hb.crm.client.dto.PackageDtos.*;
import com.hb.crm.client.dto.Results.PackageDtoResultDto;
import com.hb.crm.client.dto.Results.searchResultDto;
import com.hb.crm.client.dto.spectTrum.BookPackage;
import com.hb.crm.client.dto.spectTrum.FlightSearch.BookFlightRequst;
import com.hb.crm.client.dto.spectTrum.FlightSearch.ConfrimBookFlight;
import com.hb.crm.client.dto.spectTrum.PriceAndCheckoutUrl;
import com.hb.crm.client.services.interfaces.PackageService;
import com.hb.crm.client.services.interfaces.SubscribeService;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.SubscribeStatus;
import com.hb.crm.core.beans.FlightAndHotels;
import com.hb.crm.core.dtos.CreateMediaDto;
import com.hb.crm.core.dtos.CreateUpdatePackageDto;
import com.hb.crm.core.searchBeans.searchPackage;
import com.stripe.exception.StripeException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/v1/package", produces = {MediaType.APPLICATION_JSON_VALUE})
class PackageController {

    private final Logger logger = LoggerFactory.getLogger(PackageController.class);

    private final PackageService packageService;

    @Autowired
    private SubscribeService subscribeService;

    public PackageController(PackageService packageService) {
        this.packageService = packageService;
    }

    @GetMapping("/influencer/{influencerId}/places")
    public ResponseEntity<PageDto<PlacesDto>> getPackagePlacesByInfluencer(
            @PathVariable String influencerId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        PageDto<PlacesDto> packagePlaces = packageService.getPackagePlacesByInfluencerId(influencerId, pageable);
        return ResponseEntity.ok(packagePlaces);
    }


    @GetMapping("get/{id}")
    @ResponseBody
    public PackageDto getPackages(@PathVariable("id") String id) {
        return packageService.getPackageById(id);
    }

    @GetMapping("show-package/{slug}")
    @ResponseBody
    public PackageDto getPackageBySlug(@PathVariable("slug") String slug) {
        return packageService.getPackageBySlug(slug);
    }

    @GetMapping("getFull/{id}")
    @Operation(summary = "Getting package (Deprecated)", description = "this  api get packageWith full Details")
    @ResponseBody
    public FullPackageDto getFullPackageById(@PathVariable("id") String id) {
        return packageService.getFullPackageById(id);
    }

    @GetMapping("getPackagePosts/{id}")
    @Operation(summary = "Getting Posts", description = "this  api get posts of the package with paging")
    @ResponseBody
    public searchResultDto getPackagePosts(
            @PathVariable("id") String id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        return packageService.getPostByPackageId(id, page, size);
    }

    @GetMapping("getPackageStories/{id}")
    @Operation(summary = "Getting Stories", description = "this  api get Stories of the package with paging")
    @ResponseBody
    public searchResultDto getPackageStories(
            @PathVariable("id") String id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        return packageService.getStoriesByPackageId(id, page, size);
    }


    @GetMapping("getPackage/{id}")
    @ResponseBody
    public PackageWithItenrary getPackage(
            @PathVariable("id") String id
    ) {
        return packageService.getPackageId(id);
    }

    @GetMapping("getPackageHotels/{id}")
    @ResponseBody
    public PackageHotels getPackageHotels(
            @PathVariable("id") String id
    ) {
        return packageService.getPackageHotels(id);
    }

    @GetMapping("getActivePackage")
    @ResponseBody
    public FullPackageDto getActivePackage() {
        return packageService.getActivePackage();
    }


    @GetMapping("GetSubscribedPackages")
    @ResponseBody
    public List<searchPackage> GetSubscribedPackages() {
        return packageService.getSubscribedPackages();
    }


    @GetMapping("addPackageFav/{PackageId}")
    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @Operation(summary = "Deprecated", description = "please Use Add Reacts")
    @ResponseBody
    public ResponseEntity<SuccessMessage> AddPackageToFavourite(@PathVariable("PackageId") String PackageId) {
        //TODO resend reset password email
        packageService.AddPackageToFavourite(PackageId);
        return ResponseEntity.ok(new SuccessMessage("Subscribe Done"));
    }


    @PostMapping("ratePackage")
    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @ResponseBody
    PackageDto RatePackage(@RequestBody RateDto ratedto) {
        return packageService.RatePackage(ratedto);
    }

    @PostMapping("rateFollowMePackage")
    @ResponseBody
    PackageDto RateFollowMePackage(@RequestBody RateDto ratedto) {
        return packageService.RateFollowMePackage(ratedto);
    }


    @PostMapping("subscribeTravelWithme")
    @Operation(
            summary = "Subscribe to Travel With Me Package",
            description = """
                        This API allows users to subscribe to a **Travel With Me** package.
                        - The user must provide **package details**, **flight information**, and **preferences**.
                        - Required role: **Influencer** or **Traveler**.
                    """
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Subscription successful",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(name = "Success Response", value = """
                                        {
                                            "message": "Subscribe Done"
                                        }
                                    """))
            ),
            @ApiResponse(responseCode = "400", description = "Bad Request - Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Access denied"),
            @ApiResponse(responseCode = "500", description = "Internal Server Error")
    })
    @ResponseBody
    public ResponseEntity<SuccessMessage> subscribeTravelWithMe(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = """
                                 The request body contains the details of the travel subscription.
                                \s
                                 - **PackageId**: The package ID. Required.
                                 - **departureFlight**: Optional.
                                 - **destinationFlight**: Optional.
                                 - **travelers**: List of travelers. Required.
                                 - **roomReservations**: Selected hotels. Required.
                                 - **documents**: Per traveler. Required.
                                 - **preferences**: Optional.
                                 - **otherHotelPreferences**: Optional.
                                 - **otherFlightPreferences**: Optional.
                                \s
                                         {
                                        "packageId": "67f510ad92d8152941ad90c1",
                                        "numberOfRooms": 1,
                                        "adultCount": 0,
                                        "childCount": 0,
                                        "departureFlight": {
                                          "id": "67ad7bc67f3563308e71c013",
                                          "name": "Bintulu Airport",
                                          "code": "BTU",
                                          "country": "MY",
                                          "city": "Bintulu",
                                          "latitude": 3.1238501072,
                                          "longitude": 113.019996643
                                        },
                                        "destinationFlight": {
                                          "id": "67ad7bc27f3563308e71c00b",
                                          "name": "Batu Pahat Airport",
                                          "code": "WMAB",
                                          "country": "MY",
                                          "city": "Batu Pahat",
                                          "latitude": 1.8602420092,
                                          "longitude": 102.9010009766
                                        },
                                        "travelers": [
                                          {
                                            "dateOfBirth": "2000-03-13T15:20:16Z",
                                            "name": {
                                              "firstName": "Ahmed",
                                              "middleName": "",
                                              "lastName": "Samer"
                                            },
                                            "gender": "Male",
                                            "contact": {
                                              "emailAddress": "<EMAIL>",
                                              "phone": {
                                                "number": "+963934435685"
                                              }
                                            },
                                            "passportNumber": "ALDS43432"
                                          }
                                        ],
                                        "roomReservations": [
                                          {
                                            "hotelId": "hotel-1"
                                          }
                                        ],
                                        "documents": ["passport.pdf", "visa.pdf"],
                                        "preferences": [
                                          {
                                            "id": "67a995648515da213dde7b88",
                                            "name": "flightType",
                                            "values": ["Economy"]
                                          },
                                          {
                                            "id": "67a995478515da213dde7b87",
                                            "name": "flightTime",
                                            "values": ["morning"]
                                          },
                                          {
                                            "id": "67a995178515da213dde7b86",
                                            "name": "hotelPreferences",
                                            "values": ["5 star"]
                                          },
                                          {
                                            "id": "67b33ed786d746412944297e",
                                            "name": "roomType",
                                            "values": ["standard"]
                                          }
                                        ],
                                        "otherHotelPreferences": "Sea view room",
                                        "otherFlightPreferences": "Direct flight"
                                      }
                            
                            \s"""
            )
            @RequestBody SubscribeTravelWithMeDto subscribe
    ) throws JsonProcessingException {
        packageService.SubscribeTravelWithMePackage(subscribe);
        return ResponseEntity.ok(new SuccessMessage("Subscribe Done"));
    }


    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @GetMapping("getCheckoutURl")
    @ResponseBody
    public PriceAndCheckoutUrl CheckoutURl(@RequestParam String packageId) throws StripeException {
        //TODO resend reset password email
        return subscribeService.getCheckOutUrl(packageId);
    }


    @GetMapping("removePackageFav/{packageId}")
    @Operation(summary = "Deprecated", description = "please Use Remove Reacts")
    @ResponseBody
    public ResponseEntity<SuccessMessage> RemovePackageFavourite(@PathVariable("packageId") String PackageId) {
        //TODO resend reset password email
        packageService.RemovePackageFavourite(PackageId);
        return ResponseEntity.ok(new SuccessMessage("Package removed"));

    }


    @GetMapping("managePackageNotfiaction/{packageId}/{value}")
    @ResponseBody
    public ResponseEntity<SuccessMessage> ManagePackageNotfiaction(@PathVariable("packageId") String PackageId, @PathVariable("value") Boolean Value) {
        //TODO resend reset password email
        packageService.ManagePackageNotification(PackageId, Value);
        return ResponseEntity.ok(new SuccessMessage("Manged notification"));

    }


    @PreAuthorize("hasAnyRole('Influencer', 'Traveler')")
    @GetMapping("managePackageReaction/{packageId}/{value}")
    @ResponseBody
    public ResponseEntity<SuccessMessage> ManagePackageReaction(@PathVariable("packageId") String PackageId, @PathVariable("value") Boolean Value) {
        //TODO resend reset password email
        packageService.ManagePackageReaction(PackageId, Value);
        logger.debug("Manged Reaction ");
        return ResponseEntity.ok(new SuccessMessage("Manged Reaction"));

    }


    @GetMapping("getFavouritePackages")
    @Operation(
            summary = "Retrieve User's Favorite Packages",
            description = """
                    Returns a list of packages that the currently authenticated user has marked as favorite.
                    The list includes basic package information such as:
                    - Package ID
                    - Package name
                    - Description
                    - Destination details
                    - Price information
                    - Associated influencer details

                    Optionally supports fuzzy search within favorite packages using the 'search' parameter.
                    The search will match against package names, descriptions, influencer names, and slugs.

                    The packages are returned in order of most recently favorited first.
                    Returns an empty list if the user has no favorite packages or no matches for the search term.
                    """
    )
    @ResponseBody
    public List<PackageDto> getFavouritePackages(
            @RequestParam(value = "search", required = false) String searchTerm) {
        return packageService.GetFavouritePackages(searchTerm);
    }

    @GetMapping("getFavouritePackagesv2")
    @Operation(
            summary = "Retrieve User's Favorite Packages",
            description = """
                    Returns a list of packages that the currently authenticated user has marked as favorite.
                    The list includes basic package information such as:
                    - Package ID
                    - Package name
                    - Description
                    - Destination details
                    - Price information
                    - Associated influencer details

                    Optionally supports fuzzy search within favorite packages using the 'search' parameter.
                    The search will match against package names, descriptions, influencer names, and slugs.

                    The packages are returned in order of most recently favorited first.
                    Returns an empty list if the user has no favorite packages or no matches for the search term.
                    """
    )
    @ResponseBody
    public searchResultDto getFavouritePackagesv2(
            @RequestParam(value = "search", required = false) String searchTerm ,
            @RequestParam(value = "TravelWithMe", required = false) PackageType packageType ,
            @RequestParam(value = "page", defaultValue = "0",required = true) int page ,
            @RequestParam(value = "size", defaultValue = "10", required = true) int size)
    {
        return packageService.GetFavouritePackagesv2( page,  size ,  packageType ,searchTerm);
    }

    @PreAuthorize("hasAnyRole('Influencer')")
    @GetMapping("getRequstedPackages")
    @ResponseBody
    public List<InfluencerPackageDto> getRequstedPackages() {
        return packageService.GetRequestedPackages();
    }


    @GetMapping("list/{page}")
    @ResponseBody
    public PageDto<CountSubPackage> getPackages(@PathVariable("page") int page,
                                                @RequestParam(defaultValue = "TravelWithMe") PackageType packageTypeFilter) throws JsonProcessingException {
        return packageService.search(page, packageTypeFilter);
    }


    @PostMapping("search/{page}")
    @Operation(summary = "Deprecated", description = "please Use list/{page}")
    @ResponseBody
    public PageDto<PackageDto> searchPackages(@RequestBody Map<String, Object> obj, @PathVariable("page") int page) throws JsonProcessingException {
        return packageService.search(obj, page, 10);
    }


    @PostMapping("searchAvilabelFollowMe/{page}")
    @ResponseBody
    public PageDto<PackageDto> searchAvilabelFollowMe(@RequestBody Map<String, Object> obj, @PathVariable("page") int page) {
        return packageService.searchAvailableFollowMe(obj, page, 99999);
    }


    @PreAuthorize("hasRole('Influencer')")
    @PostMapping("RequstPackage")
    @ResponseBody
    public ResponseEntity<SuccessMessage> cupdate(@RequestBody CreateUpdatePackageDto obj) {
        return ResponseEntity.ok(packageService.update(obj));
    }


    @PreAuthorize("hasRole('Influencer')")
    @PostMapping("AddPackageReel")
    @ResponseBody
    public ResponseEntity<SuccessMessage> AddPackageReel(@RequestParam String PackageId,@RequestBody List<CreateMediaDto> medias) {
        packageService.AddReels(PackageId,medias);
        return ResponseEntity.ok(new SuccessMessage("reels Added"));
    }

    @Deprecated
    @PreAuthorize("hasRole('Influencer')")
    @PostMapping("reRequstPackage")
    @ResponseBody
    public ResponseEntity<SuccessMessage> reRequst(@RequestBody CreateUpdatePackageDto obj) {
        packageService.reRequest(obj);
        return ResponseEntity.ok(new SuccessMessage("ReRequst done"));
    }
    @PreAuthorize("hasRole('Influencer')")
    @PostMapping("reRequestPackage")
    @ResponseBody
    public ResponseEntity<SuccessMessage> reRequest(@RequestBody CreateUpdatePackageDto obj) {
        packageService.reRequest(obj);
        return ResponseEntity.ok(new SuccessMessage("ReRequst done"));
    }

    @PreAuthorize("hasRole('Influencer')")
    @GetMapping("setPackagePoosted/{id}")
    @ResponseBody
    public void setPackagePoosted(@PathVariable("id") String id) {
        packageService.postPackage(id);
    }


    @PostMapping("getRoomsForHotelsAndFlights")
    @ResponseBody
    public FlightAndHotels getRoomsForHotels(@RequestBody GetRoomForHotelPackageDto GetHotelsRoom) throws JsonProcessingException {
        return packageService.getRoomAndFlights(GetHotelsRoom);
    }


    @PostMapping("getCabinPrice")
    @ResponseBody
    public BookFlightRequst confirmFlightPrice(@RequestBody ConfrimBookFlight confirmPrices) throws JsonProcessingException {
        return packageService.confirmFlightPrice(confirmPrices);
    }


    @PostMapping("saveSelectedData")
    @ResponseBody
    public PackageDto saveSelectedData(@RequestBody BookPackage BookPackage) throws JsonProcessingException {
        return packageService.saveSelectedData(BookPackage);
    }


    @PostMapping("getPrice")
    @ResponseBody
    public PriceAndCheckoutUrl getPrice(@RequestBody BookPackage BookPackage) throws Exception {
        return packageService.getPrice(BookPackage);
    }


    @GetMapping("getFollowMePackage")
    @ResponseBody
    public FollowMePackage getFollowMePackage(@RequestParam String PackageId) {
        return packageService.getFollowMe(PackageId);
    }


    @GetMapping("TravelWithmeSuccess")
    @ResponseBody
    public RedirectView TravelWithmeSuccess(@RequestParam String subscribe, @RequestParam String userId, @RequestParam(name = "payment_intent_id") String paymentIntentId) {
        RedirectView redirectView = new RedirectView();

        try {
            subscribeService.TravelWithMeSuccess(paymentIntentId, subscribe, userId);
            redirectView.setUrl("/hb-crm-client/v1/package/paymentdone");
            return redirectView;
        } catch (Exception ex) {
            logger.error(ex.getMessage());
            redirectView.setUrl("/hb-crm-client/v1/package/cancel");
            return redirectView;
        }

    }

    @GetMapping("TravelWithmeSuccessV2")
    @ResponseBody
    public RedirectView TravelWithmeSuccessV2(@RequestParam String subscribe, @RequestParam String userId, @RequestParam(name = "payment_intent_id") String paymentIntentId) {
        RedirectView redirectView = new RedirectView();

        try {
            subscribeService.TravelWithMeSuccess(paymentIntentId, subscribe, userId);
            redirectView.setUrl("/v1/package/paymentdone");
            return redirectView;
        } catch (Exception ex) {
            logger.error(ex.getMessage());
            redirectView.setUrl("/v1/package/cancel");
            return redirectView;
        }

    }


    @GetMapping("success")
    @ResponseBody
    public RedirectView success(@RequestParam String subscribe, @RequestParam String userId, @RequestParam(name = "payment_intent_id") String paymentIntentId) {
        RedirectView redirectView = new RedirectView();

        try {
            subscribeService.success(paymentIntentId, subscribe, userId);
            redirectView.setUrl("/hb-crm-client/v1/package/paymentdone");
            return redirectView;
        } catch (Exception ex) {
            logger.error(ex.getMessage());
            redirectView.setUrl("/hb-crm-client/v1/package/cancel");
            return redirectView;
        }

    }

    @GetMapping("success2")
    @ResponseBody
    public RedirectView success2(@RequestParam String subscribe, @RequestParam String userId, @RequestParam(name = "payment_intent_id") String paymentIntentId) {
        RedirectView redirectView = new RedirectView();

        try {
            subscribeService.success(paymentIntentId, subscribe, userId);
            redirectView.setUrl("/v1/package/paymentdone");
            return redirectView;
        } catch (Exception ex) {
            logger.error(ex.getMessage());
            redirectView.setUrl("/v1/package/cancel");
            return redirectView;
        }

    }

    @GetMapping("cancel")
    @ResponseBody
    public void Cancel() {
    }

    @GetMapping("paymentdone")
    @ResponseBody
    public void paymentdone() {
    }

    @GetMapping("{id}/subscription/status")
    public ResponseEntity<SuccessMessage> getSubscriptionStatus(@PathVariable String id) {
        return ResponseEntity.ok(new SuccessMessage(packageService.getSubscriptionStatus(id).toString()));
    }



    @GetMapping("subscribed")
    @Operation(
            summary = "Get User's Subscribed Packages",
            description = """
                    Retrieves a paginated list of packages that a user has subscribed to.
                    This includes both:
                    - Direct subscriptions where the user is the primary subscriber
                    - Packages where the user is added as a traveler
                    Results are sorted by last update date in descending order.
                    Optional search parameter can be used to filter packages by name, description, or location.
                    Optional status parameter can be used to filter by subscription status.
                    """
    )
    @ResponseBody
    public ResponseEntity<PackageDtoResultDto> GetSubscribedPackages(
            @Parameter(
                    description = "User ID to get subscriptions for. If not provided, uses current authenticated user",
                    example = "65cde24816adba73ed021488"
            )
            @RequestParam(required = false) String userId,
            @Parameter(
                    description = "Page number (0-based)",
                    example = "0"
            )
            @RequestParam(defaultValue = "0") int page,
            @Parameter(
                    description = "Number of items per page",
                    example = "10"
            )
            @RequestParam(defaultValue = "10") int size,
            @Parameter(
                    description = "Optional search query to filter packages by name, description",
                    example = "dubai"
            )
            @RequestParam(required = false) String name,
            @Parameter(
                    description = "Optional status filter for subscriptions",
                    example = "paid"
            )
            @RequestParam(required = false) SubscribeStatus status,
            @Parameter(
                    description = "Optional Package Type filter ",
                    example = "TravelWithMe"
            )
            @RequestParam PackageType type
    ) {
        return ResponseEntity.ok(packageService.getSubscribePackageWithPagination(userId, name, type, status, page, size));
    }

    @GetMapping("/check-slug/{slug}")
    //swagger doc
    @Operation(
            summary = "Check Slug Uniqueness",
            description = "Checks if a given slug is unique across all posts."
    )
    @ResponseBody
    public boolean checkSlugUnique(@PathVariable String slug) {
        return packageService.isSlugUnique(slug);
    }

    @GetMapping("/influencer/search")
    public ResponseEntity<List<SimplePackageDto>> fuzzySearchPackages(
            @RequestParam(value = "influencerId") String influencerId,
            @RequestParam(value = "search", required = false) String searchTerm) {

        return ResponseEntity.ok(packageService.getInfluencerPackages(influencerId,searchTerm));
    }

}
