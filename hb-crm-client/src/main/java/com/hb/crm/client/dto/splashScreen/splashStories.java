package com.hb.crm.client.dto.splashScreen;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hb.crm.core.beans.StoryOverlay;
import com.hb.crm.core.searchBeans.MediaWrapperSearch;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class splashStories {

    private String id;
    private String text;
    private LocalDateTime created;
    @JsonIgnore
    private MediaWrapperSearch media;
    private String mediaUrl;
    private String mediaType;
    private String thumbnailUrl;
    private String videoDuration;
    private String packageId;
    private String packageName;
    private String packageSlug;
    private int commentsCount;
    private int reactsCount;
    private int viewsCount;
    private List<StoryOverlay> overlays;
    

 }
