package com.hb.crm.client.services.interfaces;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.hb.crm.client.beans.ObjectBoxEntity.PackageSyncEntity;
import com.hb.crm.client.dto.SuccessMessage;
import com.hb.crm.core.dtos.CreateMediaDto;
import com.hb.crm.client.dto.PackageDtos.*;
import com.hb.crm.client.dto.PageDto;
import com.hb.crm.client.dto.PlacesDto;
import com.hb.crm.client.dto.RateDto;
import com.hb.crm.client.dto.Results.PackageDtoResultDto;
import com.hb.crm.client.dto.Results.searchResultDto;
import com.hb.crm.client.dto.spectTrum.BookPackage;
import com.hb.crm.client.dto.spectTrum.FlightResponse.ConfirmPirceResponse;
import com.hb.crm.client.dto.spectTrum.FlightSearch.BookFlightRequst;
import com.hb.crm.client.dto.spectTrum.FlightSearch.ConfirmBookFlightRequstDto;
import com.hb.crm.client.dto.spectTrum.FlightSearch.ConfrimBookFlight;
import com.hb.crm.client.dto.spectTrum.PriceAndCheckoutUrl;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.SubscribeStatus;
import com.hb.crm.core.beans.FlightAndHotels;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.Subscribe;
import com.hb.crm.core.dtos.CreateUpdatePackageDto;
import com.hb.crm.core.searchBeans.ReactionSearch;
import com.hb.crm.core.searchBeans.searchPackage;
import com.mongodb.MongoException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface PackageService {
    void updatePackageInfo(PackageSyncEntity syncedPackageInfo);
    PageDto<CountSubPackage> search(int page, PackageType packageTypeFilter) throws JsonProcessingException;
    PageDto<PackageDto> search(Map<String, Object> obj, int page, int i) throws JsonProcessingException;
    PageDto<PackageDto> searchAvailableFollowMe(Map<String, Object> obj, int page, int i);
    PackageDto getPackageById(String id);
    FullPackageDto getFullPackageById(String id);
    searchResultDto getPostByPackageId(String id, int page, int i);
    searchResultDto getStoriesByPackageId(String id, int page, int i);
    PackageWithItenrary getPackageId(String id);
    PackageHotels getPackageHotels(String id);
    FullPackageDto getActivePackage();
    SuccessMessage update(CreateUpdatePackageDto obj);
    void AddReels(String PackageId,List<CreateMediaDto> medias);
    void reRequest(CreateUpdatePackageDto obj);
    void  AddPackageToFavourite(String PackageId);
    void  RemovePackageFavourite(String PackageId);
    void  SubscribePackage(String PackageId);
    String  SubscribeTravelWithMePackage(SubscribeTravelWithMeDto subscribe) throws JsonProcessingException;
    PriceAndCheckoutUrl getSubscribePrice(Subscribe subscribe, ArrayList<ConfirmPirceResponse> ConfirmPriceResponses) throws JsonProcessingException;
    void  UnSubscribePackage(String PackageId);
    void ManagePackageNotification(String PackageId, Boolean value);
    void  ManagePackageReaction(String PackageId,Boolean value);
    void postPackage(String PackageId);

    /**
     * Retrieves all packages marked as favorite by the current user.
     *
     * <p>This method performs the following steps:
     * <ol>
     *   <li>Retrieves the current user's ID from the session</li>
     *   <li>Queries all "Favourite" type reactions for packages associated with the user</li>
     *   <li>Extracts package IDs from the reactions</li>
     *   <li>Fetches the corresponding packages from the repository</li>
     *   <li>Converts the packages to DTOs</li>
     * </ol>
     * </p>
     *
     * @return List<PackageDto> A list of favorite packages converted to DTOs.
     *         Returns an empty list if no favorites are found.
     * @see PackageDto
     * @see ReactionSearch
     * @see SubPackage
     */
    List<PackageDto> GetFavouritePackages();

    /**
     * Retrieves packages marked as favorite by the current user with optional fuzzy search.
     *
     * <p>This method performs the following steps:
     * <ol>
     *   <li>Retrieves the current user's ID from the session</li>
     *   <li>Queries all "Favourite" type reactions for packages associated with the user</li>
     *   <li>Extracts package IDs from the reactions</li>
     *   <li>If search term is provided, performs fuzzy search within the favorite packages</li>
     *   <li>Otherwise, fetches all favorite packages from the repository</li>
     *   <li>Converts the packages to DTOs</li>
     * </ol>
     * </p>
     *
     * @param searchTerm Optional search term for fuzzy search within favorite packages.
     *                   If null or empty, returns all favorite packages.
     * @return List<PackageDto> A list of favorite packages converted to DTOs.
     *         Returns an empty list if no favorites are found or no matches for the search term.
     * @see PackageDto
     * @see ReactionSearch
     * @see SubPackage
     */
    List<PackageDto> GetFavouritePackages(String searchTerm);
    searchResultDto GetFavouritePackagesv2(int page, int size ,PackageType packageType ,String searchTerm);

    /**
     * Retrieves all packages associated with a user, including both direct subscriptions and traveler subscriptions.
     *
     * @param id The user ID to fetch packages for
     * @return A combined list of {@link PackageDto} containing both direct subscriptions and traveler subscriptions,
     *         with duplicates removed. Returns null if the operation fails.
     *         The returned packages exclude any canceled packages.
     * @throws IllegalArgumentException if id is null
     * @throws MongoException if there's an error executing the MongoDB aggregation
     */
    List<PackageDto> GetUserSubscribedPackages(String id);

    List<searchPackage> getSubscribedPackages();
    List<PackageDto> GetUserOwnedPackages( String id);
    List<InfluencerPackageDto> GetRequestedPackages();
    FlightAndHotels getRoomAndFlights(GetRoomForHotelPackageDto getRoomHotels) throws JsonProcessingException;
    List<BookFlightRequst> confirmFlightPrices(FlightAndHotels flightAndHotels,ArrayList<ConfirmBookFlightRequstDto> confirmPrices) throws JsonProcessingException;
    BookFlightRequst confirmFlightPrice(ConfrimBookFlight confirmPrices) throws JsonProcessingException;
    PriceAndCheckoutUrl getPrice(BookPackage bookPackage) throws Exception;
    PackageDto saveSelectedData(BookPackage savedBookPackage) throws JsonProcessingException;
    FollowMePackage getFollowMe(String packageId);
    PackageDto RatePackage(RateDto rate);
    PackageDto RateFollowMePackage(RateDto rate);
    SubscribeStatus getSubscriptionStatus(String id);

    /**
     * Retrieves a package by its slug and converts it to a DTO.
     * If no package is found with the given slug, this method returns {@code null}.
     *
     * @param slug The unique slug identifier of the package to retrieve
     * @return {@link PackageDto} containing the package data, or {@code null} if no package is found
     * @see PackageDto
     */
    PackageDto getPackageBySlug(String slug);

    PageDto<PlacesDto> getPackagePlacesByInfluencerId(String influencerId, Pageable pageable);

    Page<searchPackage> searchExplore(String search, int page, int size, PackageType type);


    /**
     * Retrieves a paginated list of packages that a user has subscribed to, combining both direct
     * and traveler subscriptions. The results can be optionally filtered using a search query.
     *
     * @param userId The ID of the user whose subscriptions should be retrieved. If null or empty,
     *               the current user's ID is used
     * @param page Zero-based page index for pagination
     * @param size Number of items per page
     * @param query Optional search term to filter packages. If provided, performs a text search
     *              across package attributes
     * @param subscribeStatus Filter criterion for subscription status
     * @return PageDto containing filtered searchPackage objects and total count
     * @throws IllegalArgumentException if page or size are negative
     * @throws MongoException if there's an error executing the MongoDB operations
     */
    PageDto<searchPackage> getSubscribedPackages(String userId, int page, int size, String query,
                                                 SubscribeStatus subscribeStatus);

    PackageDtoResultDto getSubscribePackageWithPagination(String id, String query,PackageType type, SubscribeStatus status, int page, int limit);

    boolean isSlugUnique(String slug);

    List<SimplePackageDto> getInfluencerPackages(String influencerId, String searchTerm);

}