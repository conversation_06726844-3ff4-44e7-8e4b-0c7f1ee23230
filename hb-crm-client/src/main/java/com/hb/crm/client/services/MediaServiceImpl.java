package com.hb.crm.client.services;

import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.config.CustomHundlerar.CustomException;
import com.hb.crm.client.dto.*;
import com.hb.crm.client.dto.MediaReaction.MediaReactionDto;
import com.hb.crm.client.dto.Results.CommentResultDto;
import com.hb.crm.client.dto.Results.MediaResultDto;
import com.hb.crm.client.dto.posts.CommentDto;
import com.hb.crm.client.services.interfaces.MediaService;
import com.hb.crm.client.services.interfaces.QueryNormalizeService;
import com.hb.crm.core.CashService.CashService;
import com.hb.crm.core.Enums.ImageCategory;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.dtos.MediaWithUserDto;
import com.hb.crm.core.dtos.RefranceModelDto;
import com.hb.crm.core.repositories.CommentRepository;
import com.hb.crm.core.repositories.MediaReactionRepository;
import com.hb.crm.core.repositories.MediaRepository;
import com.hb.crm.core.repositories.UserRepository;
import com.hb.crm.core.searchBeans.searchPackage;
import com.hb.crm.core.searchBeans.searchPost;
import com.hb.crm.core.searchBeans.searchStory;
import com.hb.crm.core.searchRepositories.SearchPackageRepository;
import com.hb.crm.core.searchRepositories.SearchUserRepository;
import com.hb.crm.core.util.ApplicationUtil;
import com.mongodb.BasicDBObject;
import org.bson.types.ObjectId;
import org.joda.time.DateTime;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class MediaServiceImpl implements MediaService {

    private final CashService cashService;
    private final MediaRepository mediaRepository;
    private final UserSessionData userSessionData;
    private final SearchUserRepository searchUserRepository;
    private final SearchPackageRepository searchPackageRepository;
    private final MediaReactionRepository mediaReactionRepository;
    private final MongoTemplate mongoTemplate;
    private final MongoTemplate mongoTemplate2;
    private final CommentRepository commentRepository;
    private final ModelMapper modelMapper;
    private final UserRepository userRepository;
    private final QueryNormalizeService queryNormalizeService;
    private Logger logger = LoggerFactory.getLogger(MediaServiceImpl.class);

    public MediaServiceImpl(CashService cashService, MediaRepository mediaRepository,
                            UserSessionData userSessionData,
                            SearchUserRepository searchUserRepository, SearchPackageRepository searchPackageRepository,
                            MediaReactionRepository mediaReactionRepository,
                            @Qualifier("mongoTemplate1") MongoTemplate mongoTemplate,
                            @Qualifier("mongoTemplate2")MongoTemplate mongoTemplate2, CommentRepository commentRepository, ModelMapper modelMapper, UserRepository userRepository, QueryNormalizeService queryNormalizeService) {
        this.cashService = cashService;
        this.mediaRepository = mediaRepository;
        this.userSessionData = userSessionData;
        this.searchUserRepository = searchUserRepository;
        this.searchPackageRepository = searchPackageRepository;
        this.mediaReactionRepository = mediaReactionRepository;
        this.mongoTemplate = mongoTemplate;
        this.mongoTemplate2 = mongoTemplate2;
        this.commentRepository = commentRepository;
        this.modelMapper = modelMapper;
        this.userRepository = userRepository;
        this.queryNormalizeService = queryNormalizeService;
    }

    @Override
    public void generateMedia(CreateMediaWrapperDto media) {
        String userId = userSessionData.getId();
        if (!userId.isEmpty()) {
            Media newMedia = new Media();
            newMedia.setTitle(media.getCaption());
            newMedia.setCreationDate(DateTime.now().toDate());
            String category = media.getUrl().split("/")[1];
            ImageCategory imageCategory = ImageCategory.valueOf(category);
            newMedia.setImageCategory(imageCategory);
            if (media.getType() == null) {
                if (isImageFile(media.getUrl())) {
                    media.setType(MediaType.image);
                    newMedia.setSource(media.getUrl());
                } else if (isVideoFile(media.getUrl())) {
                    media.setType(MediaType.video);
                    newMedia.setVideoUrl(media.getUrl());
                    if (media.getVideoSize() != null) {
                        newMedia.setVideoSize(media.getVideoSize());
                    }

                } else if (isFile(media.getUrl())) {
                    media.setType(MediaType.file);
                    newMedia.setSource(media.getUrl());
                } else {
                    throw new RuntimeException("invalid media type");
                }
            } else {
                if (isImageFile(media.getUrl())) {
                    newMedia.setSource(media.getUrl());
                } else if (isVideoFile(media.getUrl())) {
                    newMedia.setVideoUrl(media.getUrl());
                } else {
                    throw new RuntimeException("invalid media type");
                }
            }
            newMedia.setMediaType(media.getType());
            newMedia.setEmployee(false);
            newMedia.setOwnerId(userId);
            mediaRepository.save(newMedia);
            RefranceModelDto referenceModel = new RefranceModelDto();
            referenceModel.setId(newMedia.getId());
            media.setMedia(referenceModel);
        }
    }    @Override
    public void generatePostMedia(CreateMediaWrapperDto media) {
        String userId = userSessionData.getId();
        if (!userId.isEmpty()) {
            Media newMedia = new Media();
            newMedia.setTitle(media.getCaption());
            newMedia.setCreationDate(DateTime.now().toDate());
            String category = media.getUrl().split("/")[1];
            ImageCategory imageCategory = ImageCategory.valueOf(category);
            newMedia.setImageCategory(imageCategory);
            if (media.getType() == null) {
                if (isImageFile(media.getUrl())) {
                    media.setType(MediaType.image);
                    newMedia.setSource(media.getUrl());
                } else if (isVideoFile(media.getUrl())) {
                    media.setType(MediaType.reel);
                    newMedia.setVideoUrl(media.getUrl());
                    if (media.getVideoSize() != null) {
                        newMedia.setVideoSize(media.getVideoSize());
                    }

                } else if (isFile(media.getUrl())) {
                    media.setType(MediaType.file);
                    newMedia.setSource(media.getUrl());
                } else {
                    throw new RuntimeException("invalid media type");
                }
            } else {
                if (isImageFile(media.getUrl())) {
                    newMedia.setSource(media.getUrl());
                } else if (isVideoFile(media.getUrl())) {
                    newMedia.setVideoUrl(media.getUrl());
                } else {
                    throw new RuntimeException("invalid media type");
                }
            }
            newMedia.setMediaType(media.getType());
            newMedia.setEmployee(false);
            newMedia.setOwnerId(userId);
            mediaRepository.save(newMedia);
            RefranceModelDto referenceModel = new RefranceModelDto();
            referenceModel.setId(newMedia.getId());
            media.setMedia(referenceModel);
        }
    }


    public boolean checkValidFileExtension(String fileName) {
        return isImageFile(fileName) || isVideoFile(fileName);
    }

    private boolean isImageFile(String fileName) {
        String extension = getFileExtension(fileName);
        return extension != null && (extension.equalsIgnoreCase("jpg") ||
                extension.equalsIgnoreCase("jpeg") ||
                extension.equalsIgnoreCase("png") ||
                extension.equalsIgnoreCase("gif") ||
                extension.equalsIgnoreCase("bmp") ||
                extension.equalsIgnoreCase("webp") ||
                extension.equalsIgnoreCase("tiff") ||
                extension.equalsIgnoreCase("svg"));
    }

    private boolean isVideoFile(String fileName) {
        String extension = getFileExtension(fileName);
        return extension != null && (extension.equalsIgnoreCase("mp4") ||
                extension.equalsIgnoreCase("avi") ||
                extension.equalsIgnoreCase("mov") ||
                extension.equalsIgnoreCase("wmv") ||
                extension.equalsIgnoreCase("flv") ||
                extension.equalsIgnoreCase("mkv") ||
                extension.equalsIgnoreCase("webm") ||
                extension.equalsIgnoreCase("3gp"));
    }

    private boolean isFile(String fileName) {
        String extension = getFileExtension(fileName);
        return extension != null && (extension.equalsIgnoreCase("pdf"));

    }

    private String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        } else {
            return null;
        }
    }


    /**
     * Retrieves a random page of media items of type 'reel', ensuring that previously fetched items are not repeated.
     * <p>
     * This method implements a caching mechanism to track the media items a user has already seen.
     * It ensures that each subsequent request fetches new, unseen reels until the cache is reset.
     * <p>
     * The process works as follows:
     * <ul>
     *     <li>If the request is for the first page (page 0), the cached media IDs are cleared.</li>
     *     <li>Attempts to retrieve the cached list of seen media IDs for the current user.</li>
     *     <li>If no cache is found or an error occurs, initializes an empty list.</li>
     *     <li>Calls the repository method to fetch a random selection of reels, excluding previously seen ones.</li>
     *     <li>Updates the cache with the newly fetched media IDs to prevent repetition in subsequent queries.</li>
     *     <li>Returns the fetched media items as a paginated result.</li>
     * </ul>
     *
     * @param page The current page number. If `page == 0`, the cache is cleared.
     * @param size The number of media items to retrieve.
     * @return A paginated list of randomly selected reels.
     */
    @Override
    public PageDto<MediaWithUserDto> getRandomReels(int page, int size) {
        // Clear cache if the user is requesting the first page
        if (page == 0) {
            cashService.deleteData(userSessionData.getId());
        }

        List<ObjectId> seenIds;

        try {
            // Retrieve the cached list of seen media IDs
            seenIds = (List<ObjectId>) cashService.retrieveData(userSessionData.getId());
        } catch (Exception e) {
            // If an error occurs, initialize an empty list
            seenIds = new ArrayList<>();
        }

        if (seenIds == null) {
            seenIds = new ArrayList<>();
        }

        // Fetch random reels excluding seen ones
        var result = mediaRepository.findRandomReelsWithUser(size, seenIds);

        long totalCount = mediaRepository.countByMediaType(MediaType.reel);

        // Update the cache with newly fetched media IDs
        seenIds.addAll(result
                .stream()
                .map(item -> new ObjectId(item.getId()))
                .toList());

        cashService.storeData(userSessionData.getId(), seenIds, null);

        // Return paginated result
        return new PageDto<>(size, totalCount, page, result);
    }


    /**
     * Searches for media items of type 'reel' by looking into multiple sources:
     * media repository, search packages, and users associated with reels.
     * <p>
     * The search follows these steps:
     * <ul>
     *     <li>Checks if the search query is blank; if so, returns an empty page.</li>
     *     <li>Searches for reels in the `mediaRepository` based on the query.</li>
     *     <li>Searches for reels in the `searchPackageRepository` based on the query.</li>
     *     <li>Searches for users whose names or descriptions match the query.</li>
     *     <li>Finds reels posted by the matched users.</li>
     *     <li>Combines all results into a `Set` to eliminate duplicates.</li>
     *     <li>Applies manual pagination to return the requested page.</li>
     * </ul>
     *
     * @param page        The page number to retrieve.
     * @param size        The number of items per page.
     * @param searchQuery The query string to search reels by media content, packages, or users.
     * @return A paginated list of reels matching the search criteria.
     */
    @Override
    public PageDto<MediaWithUserDto> searchReels(int page, int size, String searchQuery) {
        final Pageable pageable = PageRequest.of(page, size, Sort.Direction.DESC, "updated");
        PageDto<MediaWithUserDto> pageDto = new PageDto<>();
        Criteria criteria =  Criteria.where("user").ne(null);
        criteria.and("mediaType").is(MediaType.reel);

        Aggregation aggregation = queryNormalizeService.getSearchMedia(searchQuery,pageable,criteria);
        MediaResultDto medias=   mongoTemplate.aggregate(aggregation,"media",MediaResultDto.class).getUniqueMappedResult();
        assert medias != null;
        pageDto.setTotalNoOfItems(medias.getTotalCount());
        pageDto.setItems(medias.getFilteredResults());

          return  pageDto;
    }

    @Override
    public PageDto<CommentDto> getComments(String mediaId, int pageNumber, int itemsPerPage)
    {
        final Pageable pageable = ApplicationUtil.createPageRequest(pageNumber, itemsPerPage, "createdDate", "DESC");
        long start = System.currentTimeMillis(); //start of the function
        long gettingComment = System.currentTimeMillis(); //start of the function
        long executionTime = gettingComment - start;
        logger.error("getting comment executed in {} ms", executionTime);
        final Aggregation countAggregation = queryNormalizeService.getMediaComments(Criteria.where("media._id").is(new ObjectId(mediaId)), pageable);
        AggregationResults<CommentResultDto> Results = mongoTemplate.aggregate(countAggregation, "comment", CommentResultDto.class);
        long totalCount = Results.getUniqueMappedResult() != null ? Results.getUniqueMappedResult().getTotalCount() : 0;
        List<CommentDto> comments = Results.getUniqueMappedResult().getFilteredResults();
        long gettingReacts = System.currentTimeMillis(); //start of the function
        long executionReactTime = gettingReacts - gettingComment;
        logger.error("getting comments  in {} ms", executionReactTime);
        final PageDto<CommentDto> page = new PageDto(itemsPerPage, pageNumber, "createdDate");
        page.setItems(comments);
        page.setTotalNoOfItems(totalCount);
        return page;
    }
    @Override
    public PageDto<MediaReactionDto> getMediaReactions(String mediaId, int page, int size)
    {
        // Validate and normalize pagination parameters
        int validatedPage = Math.max(page, 0);
        int validatedSize = Math.max(Math.min(size, 50), 1); // Limit size between 1 and 50
        // Create pageable object with sorting by creation date desc
        Pageable pageable = PageRequest.of(validatedPage, validatedSize,
                Sort.by(Sort.Direction.DESC, "creationDate"));
        // Get paginated results using repository
        Page<MediaReaction> reactionPage = mediaReactionRepository.findByMediaId(
            new ObjectId(mediaId),
            pageable
        );
        // Convert Spring Page to PageDto
        return new PageDto<>(
            validatedSize,
            reactionPage.getTotalElements(),
            validatedPage,
            reactionPage.getContent().stream().map(z->modelMapper.map(z,MediaReactionDto.class)).toList()
        );
    }

    @Override
    public CommentDto addComment(String mediaId, MediaCommentCreateDto commentDto)
    {
        String userId = userSessionData.getId();
        // Get the user
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found"));
        // Get the media
        Media media = mediaRepository.findById(mediaId)
                .orElseThrow(() -> new CustomException(404, "Media not found"));
        // Create and save the comment
        Comment comment = modelMapper.map(commentDto, Comment.class);
        comment.setUser(user);
        comment.setCreatedDate(LocalDateTime.now());
        comment.setMedia(media);
        // Save the comment
        Comment savedComment = commentRepository.save(comment);
        // Update media's comment count
        media.setNumberOfComments(media.getNumberOfComments() + 1);
        mediaRepository.save(media);
        syncMedia(media);
        return modelMapper.map(savedComment,CommentDto.class);
    }

    @Override
    public void deleteReel(String mediaId) {
        ObjectId objectId = new ObjectId(mediaId);

        // =========================
        // 1) Post.media (DBRef)
        // =========================
        Query queryPost = new Query();
        Update updatePost = new Update().pull("media", new BasicDBObject("media.$id", objectId));
        mongoTemplate.updateMulti(queryPost, updatePost, Post.class);

        // =========================
        // 2) Package.medias (DBRef)
        // =========================
        Query queryPackage = new Query();
        Update updatePackage = new Update().pull("medias", new BasicDBObject("media.$id", objectId));
        mongoTemplate.updateMulti(queryPackage, updatePackage, Package.class);

        // =========================
        // 3) searchPost.media (embedded SearchMedia)
        // =========================
        Query querySearchPost = new Query();
        Update updateSearchPost = new Update().pull("media", new BasicDBObject("media.id", mediaId));
        mongoTemplate2.updateMulti(querySearchPost, updateSearchPost, searchPost.class);

        // =========================
        // 4) searchPackage.medias (embedded SearchMedia)
        // =========================
        Query querySearchPackage = new Query();
        Update updateSearchPackage = new Update().pull("medias", new BasicDBObject("media.id", mediaId));
        mongoTemplate2.updateMulti(querySearchPackage, updateSearchPackage, searchPackage.class);

        // =========================
        // 5) searchStory.media (embedded SearchMedia)
        // =========================
        Query querySearchStory = new Query();
        Update updateSearchStory = new Update().pull("media", new BasicDBObject("media.id", mediaId));
        mongoTemplate2.updateMulti(querySearchStory, updateSearchStory, searchStory.class);

        // =========================
        // Finally, delete the Media document itself if needed
        // =========================
        mediaRepository.deleteById(mediaId);
    }


    @Override
    public void updateReel(String reelId, UpdateReelDto updateObj) {
        Media media = mediaRepository.findById(reelId)
                .orElseThrow(() -> new CustomException(404, "Reel not found"));

        media.setTitle(updateObj.getTitle());
        media.setTags(updateObj.getTags());
        syncMedia(media);
        mediaRepository.save(media);
    }


    private void syncMedia(Media media) {
        Query query = new Query(
                Criteria.where("medias.media._id").is(new ObjectId(media.getId()))
        );

        Query postQuery = new Query(
                Criteria.where("media.media._id").is(new ObjectId(media.getId()))
        );

        // Prepare update object for media arrays
        Update update = new Update()
                .set("medias.$.media.title", media.getTitle())
                .set("medias.$.media.creationDate", media.getCreationDate())
                .set("medias.$.media.description", media.getDescription())
                .set("medias.$.media.source", media.getSource())
                .set("medias.$.media.videoUrl", media.getVideoUrl())
                .set("medias.$.media.imageCategory", media.getImageCategory())
                .set("medias.$.media.videoDuration", media.getVideoDuration())
                .set("medias.$.media.videoDurationMS", media.getVideoDurationMS())
                .set("medias.$.media.thumbnailClipUrl", media.getThumbnailClipUrl())
                .set("medias.$.media.thumbnailCaptureUrl", media.getThumbnailCaptureUrl())
                .set("medias.$.media.mediaType", media.getMediaType())
                .set("medias.$.media.ownerId", media.getOwnerId())
                .set("medias.$.media.videoSize", media.getVideoSize())
                .set("medias.$.media.lastUpdaterId", media.getLastUpdaterId())
                .set("medias.$.media.employee", media.getEmployee())
                .set("medias.$.media.numberOfReactions", media.getNumberOfReactions())
                .set("medias.$.media.numberOfComments", media.getNumberOfComments())
                .set("medias.$.media.userInfo.id", media.getUser() != null ? media.getUser().getId() : null);

        //user
        if (media.getUser() != null && media.getUser().getUsername() != null) {
            update.set("medias.$.media.userInfo.id", media.getUser().getId());
            update.set("medias.$.media.userInfo.username", media.getUser().getUsername());
            update.set("medias.$.media.userInfo.firstName", media.getUser().getFirstName());
            update.set("medias.$.media.userInfo.lastName", media.getUser().getLastName());
            update.set("medias.$.media.userInfo.usertype", media.getUser().getUsertype());
            update.set("medias.$.media.userInfo.coverImage", media.getUser().getCoverImage());
            update.set("medias.$.media.userInfo.profileImage", media.getUser().getProfileImage());
        }
        //package
        if (media.get_package() != null && media.get_package().getSlug() != null) {
                      update
                      .set("medias.$.media._package.id", media.get_package().getId())
                      .set("medias.$.media._package.name",   media.get_package().getName() )
                      .set("medias.$.media._package.slug",  media.get_package().getSlug())
                      .set("medias.$.media._package.description",  media.get_package().getDescription());
        }

        // Prepare update object for single media in post/package
        Update updatePost = new Update()
                .set("media.$.media.title", media.getTitle())
                .set("media.$.media.creationDate", media.getCreationDate())
                .set("media.$.media.description", media.getDescription())
                .set("media.$.media.source", media.getSource())
                .set("media.$.media.videoUrl", media.getVideoUrl())
                .set("media.$.media.imageCategory", media.getImageCategory())
                .set("media.$.media.videoDuration", media.getVideoDuration())
                .set("media.$.media.videoDurationMS", media.getVideoDurationMS())
                .set("media.$.media.thumbnailClipUrl", media.getThumbnailClipUrl())
                .set("media.$.media.thumbnailCaptureUrl", media.getThumbnailCaptureUrl())
                .set("media.$.media.mediaType", media.getMediaType())
                .set("media.$.media.ownerId", media.getOwnerId())
                .set("media.$.media.videoSize", media.getVideoSize())
                .set("media.$.media.lastUpdaterId", media.getLastUpdaterId())
                .set("media.$.media.employee", media.getEmployee())
                .set("media.$.media.numberOfReactions", media.getNumberOfReactions())
                .set("media.$.media.numberOfComments", media.getNumberOfComments());
        //user
        if (media.getUser() != null && media.getUser().getUsername() != null) {
            updatePost.set("media.$.media.userInfo.id", media.getUser().getId());
            updatePost.set("media.$.media.userInfo.username", media.getUser().getUsername());
            updatePost.set("media.$.media.userInfo.firstName", media.getUser().getFirstName());
            updatePost.set("media.$.media.userInfo.lastName", media.getUser().getLastName());
            updatePost.set("media.$.media.userInfo.usertype", media.getUser().getUsertype());
            updatePost.set("media.$.media.userInfo.coverImage", media.getUser().getCoverImage());
            updatePost.set("media.$.media.userInfo.profileImage", media.getUser().getProfileImage());
        }
        //post
        if (media.getPost() != null && media.getPost().getSlug() != null) {
            updatePost.set("media.$.media.post.id", media.getPost().getId());
            updatePost.set("media.$.media.post.text", media.getPost().getText());
            updatePost.set("media.$.media.post.slug", media.getPost().getSlug());

        }
        if (media.getTags() != null &&
                !media.getTags().isEmpty() &&
                media.getTags().stream().anyMatch(tag -> tag.getText() != null)) {

            update.set("medias.$.media.tags", media.getTags());
            updatePost.set("media.$.media.tags", media.getTags());
        }
        // Run updates on all affected classes
        mongoTemplate2.updateMulti(postQuery, updatePost, searchStory.class);
        mongoTemplate2.updateMulti(postQuery, updatePost, searchPost.class);
        mongoTemplate2.updateMulti(query, update, searchPackage.class);
    }

    /**
     * Retrieves a media item along with its associated user information by the media ID.
     *
     * @param id The unique identifier of the media item to retrieve
     * @return A DTO containing both media and user information
     */
    @Override
    public MediaWithUserDto getMediaById(String id)
    {
        if (id == null) {
            return null;
        }
        return mediaRepository.findByIdWithUser(new ObjectId(id));
    }

}
