package com.hb.crm.client.dto.posts;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hb.crm.client.dto.MediaWrapperDto;
import com.hb.crm.client.dto.PackageDtos.AlphaPackage;
import com.hb.crm.client.dto.mood.MoodDto;
import com.hb.crm.client.dto.users.SimpleUserinfoDto;
import com.hb.crm.client.dto.users.UserInfoDto;
import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PostType;
import com.hb.crm.core.beans.StoryOverlay;
import com.hb.crm.core.beans.Tag;
import com.hb.crm.core.beans.User;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PostDto {
    private String id;
    private String text;
    private List<MediaWrapperDto> media;
    private UserInfoDto user;
    private PostType postType;
    private AlphaPackage Package;
    private LocalDateTime created;
    private LocalDateTime update;
    private LocalDateTime PostedDate;
    private PackageStatus postStatus = PackageStatus.draft;
    private List<SimpleUserinfoDto> mentions;
    private String description;
    private float latitude;
    private float longtuid;
    private String rejectionNote = "";
    private String place;
    private List<Tag> tags;
    @JsonIgnore
    private List<SimpleUserinfoDto> viewrs;
    private List<MoodDto> moods;
    private int commentsCount;
    private int reactsCount;
    private int viewsCount;
    private String slug;
    private List<StoryOverlay> overlays;
    private String liveStreamId;
    private boolean isLiveStream = false;
    private boolean isCurrentlyLive = false;
    private List<SimpleUserinfoDto> taggedUsers;
}
