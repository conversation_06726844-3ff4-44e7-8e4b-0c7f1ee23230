package com.hb.crm.client.dto.PackageDtos;

import com.hb.crm.client.dto.MediaWrapperDto;
import com.hb.crm.client.dto.mood.MoodDto;
import com.hb.crm.client.dto.users.UserInfoDto;
import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import com.hb.crm.core.beans.Rate;
import com.hb.crm.core.beans.Tag;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AlphaPackage {
    private String id;
    private UserInfoDto infulancer;
    private List<Tag> tags;
    private boolean favouritebyme;
    private List<MoodDto> moods;
    private List<Rate> rates;
    private MediaWrapperDto brochure;
    private MediaWrapperDto mainImage;
    private String name;
    private String description;
    private Object details;
    private List<MediaWrapperDto> medias;
    private LocalDateTime start;
    private LocalDateTime end;
    private Airport fromAirport;
    private Airport toAirport;
    private LocalDateTime availableFrom;
    private boolean availableForFollowMe;
    private List<PackageCountry> packagePlaces;
    private  boolean fromAirportInside;
    private  boolean toAirportInside;
    private String slug;

    public double getAvgRate() {
        if (this.rates == null || this.rates.size() == 0)
            return 0;

        return this.rates.stream().mapToDouble(Rate::getRate).average().orElse(0);
    }

}
