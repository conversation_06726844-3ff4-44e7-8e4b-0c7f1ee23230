package com.hb.crm.client.services.interfaces;

import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.SearchEnum;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.List;


public interface QueryNormalizeService {
    Aggregation getPostFields(Criteria criteria , Pageable pageable , Sort sort );
     Aggregation getUserFields(Criteria criteria );
    Aggregation getUserFieldsWithPosts(Criteria criteria );
    Aggregation getUsersWithRecentStories( Pageable pageable, boolean check);
    Aggregation getFollowsFields(Criteria criteria , Pageable pageable , Sort sort  );
    Aggregation getPostReactionFields(Criteria criteria);
    Aggregation getCommentReactionFields(Criteria criteria);
    Aggregation getMediaReactionFields(Criteria criteria);
    Aggregation getReplyReactionFields(Criteria criteria);
    Aggregation getPostViewFields(Criteria criteria);
    Aggregation getUsersList(Criteria criteria);
    Aggregation getSubscribePackage(Criteria criteria  );
    Aggregation getSubscribePackageWithPagination(Criteria criteria, PackageType type, String query, Pageable pageable);
    Aggregation getReplayReacts(Criteria criteria  );
    Aggregation getCommentReacts(Criteria criteria );
    Aggregation getMediaReacts(Criteria criteria );
    Aggregation getPackageReacts(Criteria criteria);
    Aggregation getTravellerSubscribePackage(Criteria criteria  );
    Aggregation getPackage(Criteria criteria , Pageable pageable   );
    Aggregation getFullPackage(Criteria criteria   );
    Aggregation getPostComments(Criteria criteria , Pageable pageable  );
    Aggregation getMediaComments(Criteria criteria, Pageable pageable);
    Aggregation getSearch(String query, SearchEnum classFilter, Pageable pageable,Criteria criteria );
    Aggregation getSearchMedia(String query, Pageable pageable,Criteria criteria );
    Aggregation getSearchInfluencer(String query, Pageable pageable);
    Aggregation getSearchUsers(Criteria criteria,String query , Pageable pageable);
    Aggregation getUsersWithRecentStories(String userId, Pageable pageable, boolean skipUpdateFilter);
    Aggregation getSearchInfluencerScored(Criteria criteria,String query, List<String> moods, List<ObjectId> followedInfluencers, Pageable pageable);
     Aggregation getUsersWithRecentStoriesSplash(Criteria criteria,String currentUserId ,int limitPerUser,Pageable pageable, boolean skipUpdateFilter);
    Aggregation getCountryCityAreaFields(Criteria criteria, Pageable pageable, Sort sort, boolean count);
    Aggregation GetRecommendedInfluencers(Criteria criteria, List<String> userMoods,
                                          List<ObjectId> followedInfluencers, Pageable pageable);
    Aggregation getUserMedia(String userId, MediaType mediaTypeFilter, Pageable pageable);
    Aggregation getSubscribePackageByID(Criteria criteria);
    Aggregation explorePackages(String searchTerm, Pageable pageable);
    Aggregation searchByNameOrPackage(String query, int skip, int limit);
    Aggregation getTaggedReel(Pageable pageable,Criteria criteria );

}
