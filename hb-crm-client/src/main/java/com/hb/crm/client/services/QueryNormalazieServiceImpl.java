package com.hb.crm.client.services;


import com.hb.crm.client.services.interfaces.QueryNormalizeService;
import com.hb.crm.core.Enums.*;
import com.hb.crm.core.searchBeans.SearchUser;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

@Service
public class QueryNormalazieServiceImpl implements QueryNormalizeService {


    @Override
    public Aggregation getPostFields(Criteria criteria, Pageable pageable, Sort sort) {
        List<AggregationOperation> operations = new ArrayList<>();


        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");
        // Perform a left outer join with the 'package' collection
        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("Package.$id")
                .foreignField("_id")
                .as("packageLookup");
        operations.add(packageLookupOperation);
        operations.add(userLookupOperation);
        operations.add(project("_id", "text", "media", "latitude", "longtuid",
                "place", "postType", "created", "update",
                "PostedDate", "tags", "postStatus", "rejectionNote",
                "note", "commentsCount", "reactsCount", "viewsCount", "overlays",
                "liveStreamId", "isLiveStream", "isCurrentlyLive")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$packageLookup._id").elementAt(0)).as("Package._id")
                .and(ArrayOperators.arrayOf("$packageLookup.name").elementAt(0)).as("Package.name")
                .and(ArrayOperators.arrayOf("$packageLookup.slug").elementAt(0)).as("Package.slug")
        );
        return getGridAggregation(criteria, pageable, sort, operations);
    }

    @Override
    public Aggregation getUsersWithRecentStories(Pageable pageable, boolean skipUpdateFilter) {
        List<AggregationOperation> operations = new ArrayList<>();
        // Calculate the date 24 hours ago
        LocalDateTime yesterday = LocalDateTime.now().minusHours(24);

        // Perform a lookup to get posts for each user
        LookupOperation postsLookupOperation = LookupOperation.newLookup()
                .from("post") // Assuming your collection is named 'posts'
                .localField("_id")
                .foreignField("user.$id")
                .as("userPosts");

        // Add the lookup operation to the pipeline
        operations.add(postsLookupOperation);

        // Use $addFields to filter the posts array
        operations.add(addFields()
                .addFieldWithValue("recentStories", new Document("$filter", new Document("input", "$userPosts")
                        .append("as", "post")
                        .append("cond", new Document("$and", Arrays.asList(
                                // Apply the postType filter always
                                new Document("$eq", Arrays.asList("$$post.postType", "Story")),
                                new Document("$eq", Arrays.asList("$$post.postStatus", "posted")),
                                // Conditionally apply the update filter based on skipUpdateFilter
                                new Document("$cond", Arrays.asList(
                                        skipUpdateFilter,  // If skipUpdateFilter is true, ignore the update filter
                                        true,  // Return true to skip the filter
                                        new Document("$gte", Arrays.asList("$$post.update", yesterday))  // Apply the update filter if skipUpdateFilter is false
                                ))
                        )))))
                .build());

        // Custom $reduce operation to find the maximum update date
        operations.add(context -> new Document("$addFields",
                new Document("latestUpdate",
                        new Document("$reduce", new Document("input", "$recentStories")
                                .append("initialValue", null)
                                .append("in", new Document("$cond", Arrays.asList(
                                        new Document("$gt", Arrays.asList("$$this.update", "$$value")),
                                        "$$this.update",
                                        "$$value"
                                )))
                        )
                )
        ));
        // Match only users who have at least one recent post
        operations.add(match(Criteria.where("recentStories").not().size(0)));
        // Sort users by the latest post update in descending order
        // Project only the necessary fields for output
        operations.add(project("_id", "firstName", "lastName", "coverImage", "profileImage", "latestUpdate","gender","username","usertype")
                .and("recentStories").as("stories")
        );



        return getGridAggregation(null, pageable, pageable.getSort(), operations);
    }

    @Override
    public Aggregation getUsersWithRecentStoriesSplash(Criteria criteria, String currentUserId,int limitPerUser, Pageable pageable, boolean skipUpdateFilter) {
        List<AggregationOperation> operations = new ArrayList<>();

        LocalDateTime yesterday = LocalDateTime.now().minusHours(24);

        if (criteria != null)
            operations.add(match(criteria));
        if (currentUserId != null && !currentUserId.isEmpty()) {
            operations.add(context -> new Document("$addFields",
                    new Document("isCurrentUser",
                            new Document("$cond", Arrays.asList(
                                    new Document("$eq", Arrays.asList("$_id", new ObjectId(currentUserId))),
                                    1,
                                    0
                            ))
                    )
            ));
        }



        // Lookup posts per user
        operations.add(LookupOperation.newLookup()
                .from("post")
                .localField("_id")
                .foreignField("user.$id")
                .as("userPosts"));

        // ✅ Lookup packages based on Package ref inside userPosts
        operations.add(LookupOperation.newLookup()
                .from("package")
                .localField("userPosts.Package.$id")
                .foreignField("_id")
                .as("Package"));

        operations.add(LookupOperation.newLookup()
                .from("media")
                .localField("userPosts.media.media.$id")
                .foreignField("_id")
                .as("Media"));

        // Filter only recent stories
        // Filter only recent stories
        operations.add(addFields()
                .addFieldWithValue("recentStories", new Document("$filter", new Document("input", "$userPosts")
                        .append("as", "post")
                        .append("cond", new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$$post.postType", "Story")),
                                new Document("$eq", Arrays.asList("$$post.postStatus", "posted")),
                                new Document("$cond", Arrays.asList(
                                        skipUpdateFilter,
                                        true,
                                        new Document("$gte", Arrays.asList("$$post.update", yesterday))
                                ))
                        ))))
                ).build());

        if(limitPerUser >0){
            // Limit recent stories to 10 per user
            operations.add(addFields()
                    .addFieldWithValue("recentStories", new Document("$slice", Arrays.asList("$recentStories", limitPerUser)))
                    .build());

        }

        // Add the latest update date from stories
        operations.add(context -> new Document("$addFields",
                new Document("latestUpdate",
                        new Document("$reduce", new Document("input", "$recentStories")
                                .append("initialValue", null)
                                .append("in", new Document("$cond", Arrays.asList(
                                        new Document("$gt", Arrays.asList("$$this.update", "$$value")),
                                        "$$this.update",
                                        "$$value"
                                )))
                        )
                )
        ));

        // Match only users who have recent stories
        operations.add(match(Criteria.where("recentStories").not().size(0)));
        if (currentUserId != null && !currentUserId.isEmpty()) {
            operations.add(Aggregation.sort(Sort.by(
                    Sort.Order.desc("isCurrentUser"),
                    Sort.Order.desc("latestUpdate")
            )));
        }else {
            operations.add(Aggregation.sort(Sort.by(
                    Sort.Order.desc("latestUpdate")
            )));
        }
        // ✅ Project stories and join correct package using $filter
        ProjectionOperation projectOp = project("_id", "firstName", "lastName", "coverImage", "profileImage", "latestUpdate", "gender", "username", "usertype")
                .and(new AggregationExpression() {
                    @Override
                    public @NotNull Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$recentStories")
                                .append("as", "story")
                                .append("in", new Document("$let", new Document()
                                        .append("vars", new Document()
                                                .append("package", new Document("$arrayElemAt", Arrays.asList(
                                                        new Document("$filter", new Document()
                                                                .append("input", "$Package")
                                                                .append("as", "pkg")
                                                                .append("cond", new Document("$eq", Arrays.asList("$$pkg._id", "$$story.Package.$id")))
                                                        ),
                                                        0
                                                )))
                                                .append("media", new Document("$arrayElemAt", Arrays.asList(
                                                        new Document("$filter", new Document()
                                                                .append("input", "$Media")
                                                                .append("as", "med")
                                                                .append("cond", new Document("$eq", Arrays.asList(
                                                                        "$$med._id",
                                                                        new Document("$arrayElemAt", Arrays.asList("$$story.media.media.$id", 0))
                                                                )))
                                                        ),
                                                        0
                                                )))
                                        )
                                        .append("in", new Document()
                                                .append("_id", "$$story._id")
                                                .append("text", "$$story.text")
                                                .append("created", "$$story.created")
                                                .append("mediaUrl", new Document("$arrayElemAt", Arrays.asList("$$story.media.url", 0)))
                                                .append("mediaType", new Document("$arrayElemAt", Arrays.asList("$$story.media.type", 0)))
                                                .append("thumbnailUrl", "$$media.thumbnailCaptureUrl")
                                                .append("videoDuration", "$$media.videoDurationMS")
                                                .append("overlays", "$$story.overlays")
                                                .append("overlays", "$$story.overlays")
                                                .append("packageId", "$$package._id")
                                                .append("viewsCount", "$$story.viewsCount")
                                                .append("commentsCount", "$$story.commentsCount")
                                                .append("reactsCount", "$$story.reactsCount")
                                                .append("packageSlug", "$$package.slug")
                                        )
                                ))
                        );
                    }
                }).as("stories");

        operations.add(projectOp);

        return getGridAggregation(null, pageable, null, operations);
    }

    /**
     * Retrieves user media aggregation based on specified filters.
     *
     * @param userId          The unique identifier of the user whose media is being retrieved
     * @param mediaTypeFilter The type of media to filter (e.g., IMAGE, VIDEO, etc.)
     * @param pageable        Pagination information for the query results
     * @return An Aggregation object configured to fetch filtered user media with pagination
     */
    public Aggregation getUserMedia(String userId, MediaType mediaTypeFilter, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Match documents where userId equals the provided userId
        operations.add(match(Criteria.where("user.$id").is(new ObjectId(userId) )));

        // Filter media by the specified mediaType
        operations.add(match(Criteria.where("mediaType").is(mediaTypeFilter)));

        operations.add(Aggregation.lookup("user", "user.$id", "_id", "user"));
        // 4. $unwind user
        operations.add(Aggregation.unwind("user", true));
        // 5. $lookup tags
        operations.add(Aggregation.lookup("tag", "tags.$id", "_id", "tags"));
        // 6. $lookup post
        operations.add(Aggregation.lookup("post", "post.$id", "_id", "post"));
        // 7. $unwind post
        operations.add(Aggregation.unwind("post", true));
        // 8. $lookup package
        operations.add(Aggregation.lookup("package", "_package.$id", "_id", "_package"));
        // 9. $unwind package
        operations.add(Aggregation.unwind("_package", true));
        // 10. $project using AggregationExpression
        ProjectionOperation projectStage = Aggregation.project()
                .and("_id").as("id")
                .and("title").as("title")
                .and("creationDate").as("creationDate")
                .and("lastUpdate").as("lastUpdate")
                .and("source").as("source")
                .and("description").as("description")
                .and("videoUrl").as("videoUrl")
                .and("imageCategory").as("imageCategory")
                .and("videoDuration").as("videoDuration")
                .and("videoDurationMS").as("videoDurationMS")
                .and("thumbnailClipUrl").as("thumbnailClipUrl")
                .and("thumbnailCaptureUrl").as("thumbnailCaptureUrl")
                .and("mediaType").as("mediaType")
                .and("OwnerId").as("OwnerId")
                .and("videoSize").as("videoSize")
                .and("LastUpdaterId").as("LastUpdaterId")
                .and("employee").as("employee")
                .and("numberOfReactions").as("numberOfReactions")
                .and("numberOfComments").as("numberOfComments")
                // user
                .and(context -> new Document("_id", new Document("$toString", "$user._id"))
                        .append("profileImage", "$user.profileImage")
                        .append("firstName", "$user.firstName")
                        .append("lastName", "$user.lastName")
                        .append("about", "$user.about")
                        .append("username", "$user.username")).as("user")
                // tags
                .and(context -> new Document("$map", new Document("input", "$tags")
                        .append("as", "tag")
                        .append("in", new Document("id", new Document("$toString", "$$tag._id"))
                                .append("text", "$$tag.text")))).as("tags")
                // postOrStory
                .and(context -> new Document("postOrStory_id", new Document("$toString", "$post._id"))
                        .append("text", "$post.text")
                        .append("slug", "$post.slug")
                        .append("description", "$post.description")
                        .append("type", "$post.type")).as("postOrStory")

                // _package
                .and(context ->
                        new Document("_id", "$_package._id")
                                .append("name", "$_package.name")
                                .append("slug", "$_package.slug")
                                .append("description", "$_package.description")).as("_package");

        operations.add(projectStage);

        // Apply pagination and sorting, with no additional criteria (null)
        return getGridAggregation(null, pageable, pageable.getSort(), operations);
    }

    public Aggregation getUsersWithRecentStories(String userId, Pageable pageable, boolean skipUpdateFilter) {
        List<AggregationOperation> operations = new ArrayList<>();
        LocalDateTime yesterday = LocalDateTime.now().minusHours(24);

        // Match the followers where the current user is the follower
        operations.add(match(Criteria.where("_id.follower.$id").is(new ObjectId(userId))));

        // Lookup the influencer details
        LookupOperation userLookup = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("influencer");
        operations.add(userLookup);

        // Unwind the influencer array to de-nest
        operations.add(unwind("influencer"));

        // Match only influencer type users
        operations.add(Aggregation.match(Criteria.where("influencer.usertype").is("Influencer")));

        // Lookup posts from these influencers
        LookupOperation postsLookup = LookupOperation.newLookup()
                .from("post")
                .localField("influencer._id")
                .foreignField("user.$id")
                .as("userPosts");
        operations.add(postsLookup);

        // Filter only valid stories
        operations.add(addFields()
                .addFieldWithValue("recentStories", new Document("$filter", new Document("input", "$userPosts")
                        .append("as", "post")
                        .append("cond", new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$$post.postType", "Story")),
                                new Document("$eq", Arrays.asList("$$post.postStatus", "posted")),
                                new Document("$cond", Arrays.asList(
                                        skipUpdateFilter,
                                        true,
                                        new Document("$gte", Arrays.asList("$$post.update", yesterday))
                                ))
                        )))))
                .build());

        // Find latest update timestamp
        operations.add(context -> new Document("$addFields",
                new Document("latestUpdate",
                        new Document("$reduce", new Document("input", "$recentStories")
                                .append("initialValue", null)
                                .append("in", new Document("$cond", Arrays.asList(
                                        new Document("$gt", Arrays.asList("$$this.update", "$$value")),
                                        "$$this.update",
                                        "$$value"
                                ))))))
        );

        // Filter users who have stories
        operations.add(match(Criteria.where("recentStories").not().size(0)));

        // Sort by latest story update
        operations.add(sort(Sort.by(Sort.Direction.DESC, "latestUpdate")));

        // Project only necessary fields
        operations.add(project("influencer._id", "influencer.firstName", "influencer.lastName","influencer.username","influencer.gender",
                "influencer.coverImage", "influencer.profileImage", "latestUpdate")
                .and("recentStories").as("stories")
        );

        return getGridAggregation(null, pageable, pageable.getSort(), operations);
    }

    @Override
    public Aggregation getCountryCityAreaFields(Criteria criteria, Pageable pageable, Sort sort, boolean count) {
        List<AggregationOperation> operations = new ArrayList<>();

        if (criteria != null) {
            operations.add(Aggregation.match(criteria));
        }

        // Lookup cities using DBRef field mapping
        LookupOperation lookupOperation = LookupOperation.newLookup()
                .from("city")
                .localField("_id")
                .foreignField("country.$id")
                .as("cities");
        operations.add(lookupOperation);

        // Explicitly map _id to id and include name and cities
        operations.add(
                Aggregation.project("name", "cities")
                        .and("_id").as("id")
        );

        return getGridAggregation2(criteria, pageable, sort, count, operations);
    }


    @Override
    public Aggregation getUserFields(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();
        if (criteria != null) {
            operations.add(match(criteria));
        }
        LookupOperation moodLookupOperation = LookupOperation.newLookup()
                .from("mood")
                .localField("moods.$id")
                .foreignField("_id")
                .as("moodLookup");
        LookupOperation PostLookupOperation = LookupOperation.newLookup()
                .from("post")
                .localField("postBookMark.$id")
                .foreignField("_id")
                .as("postLookup");
        operations.add(moodLookupOperation);
        operations.add(PostLookupOperation);
        operations.add(project("_id", "creationDate", "updatedDate", "about"
                , "username", "password", "firstName", "lastName","birthDate",
                "userInfo", "guestEmail", "AccountLockDate", "gender", "like",
                "code", "role", "collectionDeviceId", "failedLoginAttempts"
                , "accountLocked", "reasonForLockedAccount", "city", "country",
                "passResetKey", "failedPasswordChanges",
                "usertype", "EmailActivated","providers","privateProfile","lastConnectionDate"
                ,"savedReelsIds","isConnected",
                "EmailCode", "PhoneActiviated", "PhoneCode", "LookoutEnabled", "firebaseId"
                , "follwerscount", "followingcount", "fcmTokens",
                "medias", "coverImage", "profileImage")
                .and(new AggregationExpression() {
                    @Override
                    public @NotNull Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$moodLookup")
                                .append("as", "mood")
                                .append("in", new Document("Title", "$$mood.Title")
                                        .append("_id", "$$mood._id")
                                        .append("type", "$$mood.type")
                                        .append("media", new Document()
                                                .append("type", "$$mood.media.type")
                                                .append("caption", "$$mood.media.caption")
                                                .append("asset", "$$mood.media.asset")
                                                .append("url", "$$mood.media.url")
                                        )
                                        .append("mediaIcon", new Document()
                                                .append("type", "$$mood.mediaIcon.type")
                                                .append("caption", "$$mood.mediaIcon.caption")
                                                .append("asset", "$$mood.mediaIcon.asset")
                                                .append("url", "$$mood.mediaIcon.url")
                                        )

                                )
                        );
                    }
                }).as("moods")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$postLookup")
                                .append("as", "post")
                                .append("in", new Document("_id", "$$post._id"))
                        );
                    }
                }).as("postBookMark")
        );
        return newAggregation(operations);
    }

    @Override
    public Aggregation getUserFieldsWithPosts(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();
        if (criteria != null) {
            operations.add(match(criteria));
        }

        LookupOperation PostLookupOperation = LookupOperation.newLookup()
                .from("post")
                .localField("postBookMark.$id")
                .foreignField("_id")
                .as("postLookup");
        operations.add(PostLookupOperation);
        operations.add(project("_id")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$moodLookup")
                                .append("as", "mood")
                                .append("in", new Document("Title", "$$mood.Title")
                                        .append("_id", "$$mood._id")
                                        .append("type", "$$mood.type")
                                        .append("media", new Document()
                                                .append("type", "$$mood.media.type")
                                                .append("caption", "$$mood.media.caption")
                                                .append("asset", "$$mood.media.asset")
                                                .append("url", "$$mood.media.url")
                                        )
                                        .append("mediaIcon", new Document()
                                                .append("type", "$$mood.mediaIcon.type")
                                                .append("caption", "$$mood.mediaIcon.caption")
                                                .append("asset", "$$mood.mediaIcon.asset")
                                                .append("url", "$$mood.mediaIcon.url")
                                        )

                                )
                        );
                    }
                }).as("moods")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$postLookup")
                                .append("as", "post")
                                .append("in", new Document("_id", "$$post._id")
                                        .append("text", "$$post.text")
                                        .append("media", "$$post.media")
                                        .append("latitude", "$$post.latitude")
                                        .append("longtuid", "$$post.longtuid")
                                        .append("place", "$$post.place")
                                        .append("postType", "$$post.postType")
                                        .append("created", "$$post.created")
                                        .append("update", "$$post.update")
                                        .append("PostedDate", "$$post.PostedDate")
                                        .append("tags", "$$post.tags")
                                        .append("postStatus", "$$post.postStatus")
                                        .append("rejectionNote", "$$post.rejectionNote")
                                        .append("note", "$$post.note")
                                        .append("commentsCount", "$$post.commentsCount")
                                        .append("reactsCount", "$$post.reactsCount")

                                )


                        );
                    }
                }).as("postBookMark")
        );
        return newAggregation(operations);
    }

    @Override
    public Aggregation getUsersList(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();



        // Project only the required fields
        operations.add(project("_id",
                "firstName", "lastName",
                "email", "mobile", "gender",
                "city", "country", "usertype","username",
                "follwerscount", "followingcount",
                "coverImage", "profileImage"));

        // Apply sort from the pageable
        if (criteria != null) {
            operations.add(match(criteria));
        }
        return Aggregation.newAggregation(operations);
    }


    @Override
    public Aggregation getFollowsFields(Criteria criteria, Pageable pageable, Sort sort) {

        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");

        LookupOperation followerLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.follower.$id")
                .foreignField("_id")
                .as("followerLookup");
        operations.add(userLookupOperation);
        operations.add(followerLookupOperation);
        operations.add(project("like", "usertype",

                "medias", "coverImage", "profileImage")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("_id.user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.follwerscount").elementAt(0)).as("_id.user.follwerscount")
                .and(ArrayOperators.arrayOf("$userLookup.followingcount").elementAt(0)).as("_id.user.followingcount")
                .and(ArrayOperators.arrayOf("$userLookup.userInfo").elementAt(0)).as("_id.user.userInfo")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("_id.user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("_id.user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.about").elementAt(0)).as("_id.user.about")
                .and(ArrayOperators.arrayOf("$userLookup.userInfo").elementAt(0)).as("_id.user.userInfo")
                .and(ArrayOperators.arrayOf("$userLookup.guestEmail").elementAt(0)).as("_id.user.guestEmail")
                .and(ArrayOperators.arrayOf("$userLookup.city").elementAt(0)).as("_id.user.city")
                .and(ArrayOperators.arrayOf("$userLookup.country").elementAt(0)).as("_id.user.country")
                .and(ArrayOperators.arrayOf("$userLookup.medias").elementAt(0)).as("_id.user.medias")
                .and(ArrayOperators.arrayOf("$userLookup.gender").elementAt(0)).as("_id.user.gender")
                .and(ArrayOperators.arrayOf("$userLookup.stories").elementAt(0)).as("_id.user.stories")
                .and(ArrayOperators.arrayOf("$userLookup.FollowsCount").elementAt(0)).as("_id.user.FollowsCount")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("_id.user.usertype")


                .and(ArrayOperators.arrayOf("$followerLookup._id").elementAt(0)).as("_id.follower._id")
                .and(ArrayOperators.arrayOf("$followerLookup.profileImage").elementAt(0)).as("_id.follower.profileImage")
                .and(ArrayOperators.arrayOf("$followerLookup.firstName").elementAt(0)).as("_id.follower.firstName")
                .and(ArrayOperators.arrayOf("$followerLookup.lastName").elementAt(0)).as("_id.follower.lastName")
                .and(ArrayOperators.arrayOf("$followerLookup.userInfo").elementAt(0)).as("_id.follower.userInfo")
                 .and(ArrayOperators.arrayOf("$followerLookup.username").elementAt(0)).as("_id.follower.username")
                 .and(ArrayOperators.arrayOf("$followerLookup.usertype").elementAt(0)).as("_id.follower.usertype")

                .and(ArrayOperators.arrayOf("$followerLookup.followingcount").elementAt(0)).as("_id.follower.followingcount")
                .and(ArrayOperators.arrayOf("$followerLookup.follwerscount").elementAt(0)).as("_id.follower.follwerscount"));


        return getGridAggregation(criteria, pageable, sort, operations);
    }

    public Aggregation getTopInfluencersQuery(Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(sort(Sort.by(
                Sort.Order.desc("finalScore")
        )));

        return getGridAggregation(Criteria.where("usertype").is(UserType.Influencer), pageable, null, operations);
    }

    @NotNull
    private Aggregation getGridAggregation(Criteria criteria, Pageable pageable, Sort sort, List<AggregationOperation> operations) {
        if (criteria != null)
            operations.add(match(criteria));
        if (sort != null)
            operations.add(sort(sort));
        if (pageable != null)
            operations.addAll(generateSkipStage(pageable.getOffset(), pageable.getPageSize()));

        return newAggregation(operations);
    }

    private List<AggregationOperation> generateSkipStage(long skip, long limit) {
        List<AggregationOperation> operations = new ArrayList<>();
        // Create the $skip stage if skip is greater than 0
        List<Document> skipStage = new ArrayList<>();
        if (skip > 0) {
            skipStage.add(new Document("$skip", skip));
        }

        // Create the $limit stage
        Document limitStage = new Document("$limit", limit);

        // Combine $skip and $limit stages
        List<Document> skipLimitStages = new ArrayList<>(skipStage);
        skipLimitStages.add(limitStage);

        // Create the $facet stage for Data and TotalCount as subPage Entity
        AggregationOperation facetStage = new AggregationOperation() {
            @Override
            public Document toDocument(AggregationOperationContext context) {
                return new Document("$facet", new Document()
                        .append("filteredResults", skipLimitStages)
                        .append("TotalCount", List.of(new Document("$count", "TotalCount"))));
            }
        };
        AggregationOperation projectStage = new AggregationOperation() {
            @Override
            public Document toDocument(AggregationOperationContext context) {
                return new Document("$project", new Document()
                        .append("filteredResults", 1)
                        .append("totalCount", new Document("$arrayElemAt", List.of("$TotalCount.TotalCount", 0))));
            }
        };
        operations.add(facetStage);
        operations.add(projectStage);
        // Create the $project stage to extract Data and TotalCount we have to project it since total count is arrayElement

        // Combine all stages

        return operations;
    }

    @Override
    public Aggregation getPostReactionFields(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");

        // Perform a left outer join with the 'package' collection
        LookupOperation postLookupOperation = LookupOperation.newLookup()
                .from("post")
                .localField("post.$id")
                .foreignField("_id")
                .as("postLookup");
        operations.add(postLookupOperation);
        operations.add(userLookupOperation);

        operations.add(project("_id")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                 .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$postLookup._id").elementAt(0)).as("post._id")

        );


        if (criteria != null) {
            operations.add(match(criteria));
        }


        return newAggregation(operations);
    }


    @Override
    public Aggregation getCommentReactionFields(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");

        // Perform a left outer join with the 'package' collection
        LookupOperation commentLookupOperation = LookupOperation.newLookup()
                .from("comment")
                .localField("comment.$id")
                .foreignField("_id")
                .as("commentLookup");
        operations.add(commentLookupOperation);
        operations.add(userLookupOperation);

        operations.add(project("_id")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$commentLookup._id").elementAt(0)).as("comment._id")

        );


        if (criteria != null) {
            operations.add(match(criteria));
        }


        return newAggregation(operations);
    }

    @Override
    public Aggregation getMediaReactionFields(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");

        // Perform a left outer join with the 'package' collection
        LookupOperation commentLookupOperation = LookupOperation.newLookup()
                .from("media")
                .localField("media.$id")
                .foreignField("_id")
                .as("mediaLookup");
        operations.add(commentLookupOperation);
        operations.add(userLookupOperation);

        operations.add(project("_id")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$mediaLookup._id").elementAt(0)).as("media._id")

        );


        if (criteria != null) {
            operations.add(match(criteria));
        }


        return newAggregation(operations);
    }

    @Override
    public Aggregation getPostViewFields(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");


        // Perform a left outer join with the 'package' collection

        LookupOperation storyLookup = LookupOperation.newLookup()
                .from("post")
                .localField("_id.story.$id")
                .foreignField("_id")
                .as("storyLookup");
        operations.add(storyLookup);
        operations.add(userLookupOperation);

        operations.add(project()
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$storyLookup._id").elementAt(0)).as("_id.story._id")

        );


        if (criteria != null) {
            operations.add(match(criteria));
        }


        return newAggregation(operations);
    }

    @Override
    public Aggregation getReplyReactionFields(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");

        // Perform a left outer join with the 'package' collection
        LookupOperation replyLookupOperation = LookupOperation.newLookup()
                .from("reply")
                .localField("reply.$id")
                .foreignField("_id")
                .as("replyLookup");
        operations.add(replyLookupOperation);
        operations.add(userLookupOperation);

        operations.add(project("_id")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$replyLookup._id").elementAt(0)).as("reply._id")

        );


        if (criteria != null) {
            operations.add(match(criteria));
        }


        return newAggregation(operations);
    }
     private List<AggregationOperation> getSubscribePackageOperations() {
        List<AggregationOperation> operations = new ArrayList<>();


        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");


        // Perform a left outer join with the 'package' collection

        LookupOperation subBackageLookup = LookupOperation.newLookup()
                .from("subPackage")
                .localField("_id._package.$id")
                .foreignField("_id")
                .as("subPackageLookup");

        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("subPackageLookup._package.$id")
                .foreignField("_id")
                .as("packageLookup");

        LookupOperation moodLookupOperation = LookupOperation.newLookup()
                .from("mood")
                .localField("packageLookup.moods.$id")
                .foreignField("_id")
                .as("moodLookup");

        LookupOperation tagLookupOperation = LookupOperation.newLookup()
                .from("tag")
                .localField("packageLookup.tags.$id")
                .foreignField("_id")
                .as("tagLookup");

        LookupOperation infulancerLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("packageLookup.infulancer.$id")
                .foreignField("_id")
                .as("infulancerLookup");

        operations.add(userLookupOperation);
        operations.add(subBackageLookup);
        operations.add(packageLookupOperation);
        operations.add(moodLookupOperation);
        operations.add(tagLookupOperation);
        operations.add(infulancerLookupOperation);

        return operations;
    }
    @Override
    public Aggregation getSubscribePackage(Criteria criteria) {
        List<AggregationOperation> operations = getSubscribePackageOperations();
        operations.add(project("total", "expireDate", "status")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("subPackageLookup._id").elementAt(0)).as("_id._package._id")
                .and(ArrayOperators.arrayOf("subPackageLookup.name").elementAt(0)).as("_id._package.name")
                .and(ArrayOperators.arrayOf("subPackageLookup.state").elementAt(0)).as("_id._package.state")
                .and(ArrayOperators.arrayOf("subPackageLookup.start").elementAt(0)).as("_id._package.start")
                .and(ArrayOperators.arrayOf("subPackageLookup.end").elementAt(0)).as("_id._package.end")
                .and(ArrayOperators.arrayOf("subPackageLookup.creationDate").elementAt(0)).as("_id._package.creationDate")
                .and(ArrayOperators.arrayOf("subPackageLookup.activities").elementAt(0)).as("_id._package.activities")
                .and(ArrayOperators.arrayOf("subPackageLookup.packageType").elementAt(0)).as("_id._package.packageType")
                .and(ArrayOperators.arrayOf("subPackageLookup.subscribeCount").elementAt(0)).as("_id._package.subscribeCount")
                .and(ArrayOperators.arrayOf("subPackageLookup.capacity").elementAt(0)).as("_id._package.capacity")
                .and(ArrayOperators.arrayOf("packageLookup.description").elementAt(0)).as("_id._package._package.description")
                .and(ArrayOperators.arrayOf("packageLookup._id").elementAt(0)).as("_id._package._package._id")
                .and(ArrayOperators.arrayOf("packageLookup.name").elementAt(0)).as("_id._package._package.name")
                .and(ArrayOperators.arrayOf("packageLookup.latitude").elementAt(0)).as("_id._package._package.latitude")
                .and(ArrayOperators.arrayOf("packageLookup.longtuid").elementAt(0)).as("_id._package._package.longtuid")
                .and(ArrayOperators.arrayOf("packageLookup.latitudeTo").elementAt(0)).as("_id._package._package.latitudeTo")
                .and(ArrayOperators.arrayOf("packageLookup.longtuidTo").elementAt(0)).as("_id._package._package.longtuidTo")
                .and(ArrayOperators.arrayOf("packageLookup.Place").elementAt(0)).as("_id._package._package.Place")
                .and(ArrayOperators.arrayOf("packageLookup.ToPlace").elementAt(0)).as("_id._package._package.ToPlace")
                .and(ArrayOperators.arrayOf("packageLookup.availableForFollowMe").elementAt(0)).as("_id._package._package.availableForFollowMe")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$moodLookup")
                                .append("as", "mood")
                                .append("in", new Document("Title", "$$mood.Title")
                                        .append("_id", "$$mood._id")
                                        .append("type", "$$mood.type")
                                        .append("media", new Document()
                                                .append("type", "$$mood.media.type")
                                                .append("caption", "$$mood.media.caption")
                                                .append("asset", "$$mood.media.asset")
                                                .append("url", "$$mood.media.url")
                                        )
                                        .append("mediaIcon", new Document()
                                                .append("type", "$$mood.mediaIcon.type")
                                                .append("caption", "$$mood.mediaIcon.caption")
                                                .append("asset", "$$mood.mediaIcon.asset")
                                                .append("url", "$$mood.mediaIcon.url")
                                        )

                                )
                        );
                    }
                }).as("_id._package._package.moods")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$tagLookup")
                                .append("as", "tag")
                                .append("in", new Document("text", "$$tag.text")
                                        .append("_id", "$$tag._id")


                                )
                        );
                    }
                }).as("_id._package._package.tags")
        );
        if (criteria != null) {
            operations.add(match(criteria));
        }
        return newAggregation(operations);
    }
    @Override
    public Aggregation  getSubscribePackageWithPagination(Criteria criteria,PackageType type,String query,Pageable pageable) {
        List<AggregationOperation> operations = getSubscribePackageOperations();

        if (criteria != null)
            operations.add(match(criteria));
        operations.add(project()
                .and(ArrayOperators.arrayOf("subPackageLookup._id").elementAt(0)).as("_id")
                .and(ArrayOperators.arrayOf("subPackageLookup.name").elementAt(0)).as("name")
                .and(ArrayOperators.arrayOf("subPackageLookup.state").elementAt(0)).as("state")
                .and(ArrayOperators.arrayOf("subPackageLookup.start").elementAt(0)).as("start")
                .and(ArrayOperators.arrayOf("subPackageLookup.end").elementAt(0)).as("end")
                .and(ArrayOperators.arrayOf("subPackageLookup.creationDate").elementAt(0)).as("creationDate")
                .and(ArrayOperators.arrayOf("subPackageLookup.activities").elementAt(0)).as("activities")
                .and(ArrayOperators.arrayOf("subPackageLookup.packageType").elementAt(0)).as("packageType")
                .and(ArrayOperators.arrayOf("subPackageLookup.subscribeCount").elementAt(0)).as("subscribeCount")
                .and(ArrayOperators.arrayOf("subPackageLookup.capacity").elementAt(0)).as("capacity")
                .and(ArrayOperators.arrayOf("packageLookup.description").elementAt(0)).as("_package.description")
                .and(ArrayOperators.arrayOf("packageLookup._id").elementAt(0)).as("_package._id")
                .and(ArrayOperators.arrayOf("packageLookup.name").elementAt(0)).as("_package.name")
                .and(ArrayOperators.arrayOf("packageLookup.latitude").elementAt(0)).as("_package.latitude")
                .and(ArrayOperators.arrayOf("packageLookup.longtuid").elementAt(0)).as("_package.longtuid")
                .and(ArrayOperators.arrayOf("packageLookup.latitudeTo").elementAt(0)).as("_package.latitudeTo")
                .and(ArrayOperators.arrayOf("packageLookup.longtuidTo").elementAt(0)).as("_package.longtuidTo")
                .and(ArrayOperators.arrayOf("packageLookup.Place").elementAt(0)).as("_package.Place")
                .and(ArrayOperators.arrayOf("packageLookup.ToPlace").elementAt(0)).as("_package.ToPlace")
                .and(ArrayOperators.arrayOf("infulancerLookup._id").elementAt(0)).as("_package.infulancer._id")
                .and(ArrayOperators.arrayOf("infulancerLookup.firstName").elementAt(0)).as("_package.infulancer.firstName")
                .and(ArrayOperators.arrayOf("infulancerLookup.lastName").elementAt(0)).as("_package.infulancer.lastName")
                .and(ArrayOperators.arrayOf("infulancerLookup.profileImage").elementAt(0)).as("_package.infulancer.profileImage")
                .and(ArrayOperators.arrayOf("infulancerLookup.coverImage").elementAt(0)).as("_package.infulancer.coverImage")
                .and(ArrayOperators.arrayOf("infulancerLookup.privateProfile").elementAt(0)).as("_package.infulancer.privateProfile")
                .and(ArrayOperators.arrayOf("infulancerLookup.gender").elementAt(0)).as("_package.infulancer.gender")
                .and(ArrayOperators.arrayOf("infulancerLookup.birthDate").elementAt(0)).as("_package.infulancer.birthDate")
                .and(ArrayOperators.arrayOf("packageLookup.availableForFollowMe").elementAt(0)).as("_package.availableForFollowMe")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$moodLookup")
                                .append("as", "mood")
                                .append("in", new Document("Title", "$$mood.Title")
                                        .append("_id", "$$mood._id")
                                        .append("type", "$$mood.type")
                                        .append("media", new Document()
                                                .append("type", "$$mood.media.type")
                                                .append("caption", "$$mood.media.caption")
                                                .append("asset", "$$mood.media.asset")
                                                .append("url", "$$mood.media.url")
                                        )
                                        .append("mediaIcon", new Document()
                                                .append("type", "$$mood.mediaIcon.type")
                                                .append("caption", "$$mood.mediaIcon.caption")
                                                .append("asset", "$$mood.mediaIcon.asset")
                                                .append("url", "$$mood.mediaIcon.url")
                                        )

                                )
                        );
                    }
                }).as("_package.moods")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$tagLookup")
                                .append("as", "tag")
                                .append("in", new Document("text", "$$tag.text")
                                        .append("_id", "$$tag._id")


                                )
                        );
                    }
                }).as("_package.tags")
        );
// MongoDB Atlas Full-Text Search - Wildcard search across all fields
        if(query!=null && !query.isEmpty()){
            Criteria queryCriteria = new Criteria();
            queryCriteria.orOperator(
                   Criteria.where("name").regex(query, "i"),
                   Criteria.where("_package.description").regex(query, "i"),
                   Criteria.where("_package.infulancer.firstName").regex(query, "i"),
                   Criteria.where("_package.infulancer.lastName").regex(query, "i")
            );
            operations.add(match(queryCriteria));
        }
        operations.add(match(Criteria.where("state").ne(1)));
        if(type!=null) {
            operations.add(match(Criteria.where("packageType").is(type)));
        }
       return getGridAggregation(null,pageable,pageable.getSort(),operations);
    }
    @Override
    public Aggregation getReplayReacts(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation replyLookupOperation = LookupOperation.newLookup()
                .from("reply")
                .localField("reply.$id")
                .foreignField("_id")
                .as("replyLookup");
        operations.add(userLookupOperation);
        operations.add(replyLookupOperation);

        if (criteria != null) {
            operations.add(match(criteria));
        }
        operations.add(project("_id", "reactionType")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("replyLookup._id").elementAt(0)).as("reply._id"));

        return newAggregation(operations);
    }

    @Override
    public Aggregation getCommentReacts(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation commentLookupOperation = LookupOperation.newLookup()
                .from("comment")
                .localField("comment.$id")
                .foreignField("_id")
                .as("commentLookup");
        operations.add(userLookupOperation);
        operations.add(commentLookupOperation);


        operations.add(project("_id", "reactionType")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("commentLookup._id").elementAt(0)).as("comment._id"));

        if (criteria != null) {
            operations.add(match(criteria));
        }
        return newAggregation(operations);
    }


    @Override
    public Aggregation getMediaReacts(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation commentLookupOperation = LookupOperation.newLookup()
                .from("media")
                .localField("media.$id")
                .foreignField("_id")
                .as("mediaLookup");
        operations.add(userLookupOperation);
        operations.add(commentLookupOperation);


        operations.add(project("_id", "reactionType")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("mediaLookup._id").elementAt(0)).as("media._id"));

        if (criteria != null) {
            operations.add(match(criteria));
        }
        return newAggregation(operations);
    }

    @Override
    public Aggregation getPackageReacts(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation commentLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("_id._package.$id")
                .foreignField("_id")
                .as("packageLookup");
        operations.add(userLookupOperation);
        operations.add(commentLookupOperation);


        operations.add(project("loveIt")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("packageLookup._id").elementAt(0)).as("_id._package._id"));

        if (criteria != null) {
            operations.add(match(criteria));
        }
        return newAggregation(operations);
    }

    @Override
    public Aggregation getTravellerSubscribePackage(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();


        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");


        // Perform a left outer join with the 'package' collection
        LookupOperation subscribeLookup = LookupOperation.newLookup()
                .from("subscribe")
                .localField("subscribe.$id")
                .foreignField("_id")
                .as("subscribeLookup");

        LookupOperation subBackageLookup = LookupOperation.newLookup()
                .from("subPackage")
                .localField("subscribeLookup._id._package.$id")
                .foreignField("_id")
                .as("subPackageLookup");

        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("subPackageLookup._package.$id")
                .foreignField("_id")
                .as("packageLookup");

        LookupOperation moodLookupOperation = LookupOperation.newLookup()
                .from("mood")
                .localField("packageLookup.moods.$id")
                .foreignField("_id")
                .as("moodLookup");

        LookupOperation tagLookupOperation = LookupOperation.newLookup()
                .from("tag")
                .localField("packageLookup.tags.$id")
                .foreignField("_id")
                .as("tagLookup");

        operations.add(userLookupOperation);
        operations.add(subscribeLookup);
        operations.add(subBackageLookup);
        operations.add(packageLookupOperation);
        operations.add(moodLookupOperation);
        operations.add(tagLookupOperation);

        operations.add(project()
                .and(ArrayOperators.arrayOf("$subscribeLookup.total").elementAt(0)).as("subscribe.total")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("subPackageLookup._id").elementAt(0)).as("subscribe._id._package._id")
                .and(ArrayOperators.arrayOf("subPackageLookup.name").elementAt(0)).as("subscribe._id._package.name")
                .and(ArrayOperators.arrayOf("subPackageLookup.state").elementAt(0)).as("subscribe._id._package.state")
                .and(ArrayOperators.arrayOf("subPackageLookup.start").elementAt(0)).as("subscribe._id._package.start")
                .and(ArrayOperators.arrayOf("subPackageLookup.end").elementAt(0)).as("subscribe._id._package.end")
                .and(ArrayOperators.arrayOf("subPackageLookup.creationDate").elementAt(0)).as("subscribe._id._package.creationDate")
                .and(ArrayOperators.arrayOf("subPackageLookup.subscribeCount").elementAt(0)).as("subscribe._id._package.subscribeCount")
                .and(ArrayOperators.arrayOf("subPackageLookup.capacity").elementAt(0)).as("subscribe._id._package.capacity")
                .and(ArrayOperators.arrayOf("subPackageLookup.packageType").elementAt(0)).as("subscribe._id._package.packageType")
                .and(ArrayOperators.arrayOf("subPackageLookup.activities").elementAt(0)).as("subscribe._id._package.activities")
                .and(ArrayOperators.arrayOf("packageLookup.description").elementAt(0)).as("subscribe._id._package._package.description")
                .and(ArrayOperators.arrayOf("packageLookup._id").elementAt(0)).as("subscribe._id._package._package._id")
                .and(ArrayOperators.arrayOf("packageLookup.name").elementAt(0)).as("subscribe._id._package._package.name")
                .and(ArrayOperators.arrayOf("packageLookup.latitude").elementAt(0)).as("subscribe._id._package._package.latitude")
                .and(ArrayOperators.arrayOf("packageLookup.longtuid").elementAt(0)).as("subscribe._id._package._package.longtuid")
                .and(ArrayOperators.arrayOf("packageLookup.latitudeTo").elementAt(0)).as("subscribe._id._package._package.latitudeTo")
                .and(ArrayOperators.arrayOf("packageLookup.longtuidTo").elementAt(0)).as("subscribe._id._package._package.longtuidTo")
                .and(ArrayOperators.arrayOf("packageLookup.Place").elementAt(0)).as("subscribe._id._package._package.Place")
                .and(ArrayOperators.arrayOf("packageLookup.ToPlace").elementAt(0)).as("subscribe._id._package._package.ToPlace")
                .and(ArrayOperators.arrayOf("packageLookup.availableForFollowMe").elementAt(0)).as("subscribe._id._package._package.availableForFollowMe")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$moodLookup")
                                .append("as", "mood")
                                .append("in", new Document("Title", "$$mood.Title")
                                        .append("_id", "$$mood._id")
                                        .append("type", "$$mood.type")
                                        .append("media", new Document()
                                                .append("type", "$$mood.media.type")
                                                .append("caption", "$$mood.media.caption")
                                                .append("asset", "$$mood.media.asset")
                                                .append("url", "$$mood.media.url")
                                        )
                                        .append("mediaIcon", new Document()
                                                .append("type", "$$mood.mediaIcon.type")
                                                .append("caption", "$$mood.mediaIcon.caption")
                                                .append("asset", "$$mood.mediaIcon.asset")
                                                .append("url", "$$mood.mediaIcon.url")
                                        )

                                )
                        );
                    }
                }).as("subscribe._id._package._package.moods")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$tagLookup")
                                .append("as", "tag")
                                .append("in", new Document("text", "$$tag.text")
                                        .append("_id", "$$tag._id")


                                )
                        );
                    }
                }).as("subscribe._id._package._package.tags")
        );


        if (criteria != null) {
            operations.add(match(criteria));
        }


        return newAggregation(operations);
    }

    @Override
    public Aggregation getPackage(Criteria criteria, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();


        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("_package.$id")
                .foreignField("_id")
                .as("packageLookup");
        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("packageLookup.infulancer.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation moodLookupOperation = LookupOperation.newLookup()
                .from("mood")
                .localField("packageLookup.moods.$id")
                .foreignField("_id")
                .as("moodLookup");

        LookupOperation tagLookupOperation = LookupOperation.newLookup()
                .from("tag")
                .localField("packageLookup.tags.$id")
                .foreignField("_id")
                .as("tagLookup");
        operations.add(packageLookupOperation);

        operations.add(userLookupOperation);
        operations.add(moodLookupOperation);
        operations.add(tagLookupOperation);

        operations.add(project("_id", "name", "state", "start", "end", "subscribeCount", "capacity", "packageStatus", "packageType", "totalPrice", "numberOfRoom", "creationDate")
                .and(ArrayOperators.arrayOf("$packageLookup.name").elementAt(0)).as("_package.name")
                .and(ArrayOperators.arrayOf("$packageLookup.latitude").elementAt(0)).as("_package.latitude")
                .and(ArrayOperators.arrayOf("$packageLookup.longtuid").elementAt(0)).as("_package.longtuid")
                .and(ArrayOperators.arrayOf("$packageLookup.latitudeTo").elementAt(0)).as("_package.latitudeTo")
                .and(ArrayOperators.arrayOf("$packageLookup.longtuidTo").elementAt(0)).as("_package.longtuidTo")
                .and(ArrayOperators.arrayOf("$packageLookup.medias").elementAt(0)).as("_package.medias")
                .and(ArrayOperators.arrayOf("$packageLookup.Place").elementAt(0)).as("_package.Place")
                .and(ArrayOperators.arrayOf("$packageLookup.ToPlace").elementAt(0)).as("_package.ToPlace")
                .and(ArrayOperators.arrayOf("$packageLookup.rates").elementAt(0)).as("_package.rates")
                .and(ArrayOperators.arrayOf("$packageLookup.slug").elementAt(0)).as("_package.slug")
                .and(ArrayOperators.arrayOf("$packageLookup.availableForFollowMe").elementAt(0)).as("_package.availableForFollowMe")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_package.infulancer._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_package.infulancer.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_package.infulancer.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$userLookup.follwerscount").elementAt(0)).as("_package.infulancer.follwerscount")
                .and(ArrayOperators.arrayOf("$packageLookup.description").elementAt(0)).as("_package.description")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$moodLookup")
                                .append("as", "mood")
                                .append("in", new Document("Title", "$$mood.Title")
                                        .append("_id", "$$mood._id")
                                        .append("type", "$$mood.type")
                                        .append("media", new Document()
                                                .append("type", "$$mood.media.type")
                                                .append("caption", "$$mood.media.caption")
                                                .append("asset", "$$mood.media.asset")
                                                .append("url", "$$mood.media.url")
                                        )
                                        .append("mediaIcon", new Document()
                                                .append("type", "$$mood.mediaIcon.type")
                                                .append("caption", "$$mood.mediaIcon.caption")
                                                .append("asset", "$$mood.mediaIcon.asset")
                                                .append("url", "$$mood.mediaIcon.url")
                                        )

                                )
                        );
                    }
                }).as("_package.moods")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$tagLookup")
                                .append("as", "tag")
                                .append("in", new Document("text", "$$tag.text")
                                        .append("_id", "$$tag._id")


                                )
                        );
                    }
                }).as("_package.tags")
        );


        if (pageable != null) {
            return getGridAggregation(criteria, pageable, pageable.getSort(), operations);

        } else {
            return getGridAggregation(criteria, null, null, operations);

        }

    }

    @Override
    public Aggregation getFullPackage(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();


        LookupOperation packageLookupOperation = LookupOperation.newLookup()
                .from("package")
                .localField("_package.$id")
                .foreignField("_id")
                .as("packageLookup");
        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("packageLookup.infulancer.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation moodLookupOperation = LookupOperation.newLookup()
                .from("mood")
                .localField("packageLookup.moods.$id")
                .foreignField("_id")
                .as("moodLookup");

        LookupOperation tagLookupOperation = LookupOperation.newLookup()
                .from("tag")
                .localField("packageLookup.tags.$id")
                .foreignField("_id")
                .as("tagLookup");
        operations.add(packageLookupOperation);

        operations.add(userLookupOperation);
        operations.add(moodLookupOperation);
        operations.add(tagLookupOperation);

        operations.add(project("_id", "name", "state", "start", "end", "subscribeCount", "activities", "capacity", "BusinessFLight", "expierDate", "flights", "alreadyActive", "hotels", "EconomicFLight", "packageStatus", "packageType", "packagePlaces", "totalPrice", "numberOfRoom", "creationDate")
                .and(ArrayOperators.arrayOf("$packageLookup.name").elementAt(0)).as("_package.name")
                .and(ArrayOperators.arrayOf("$packageLookup._id").elementAt(0)).as("_package._id")
                .and(ArrayOperators.arrayOf("$packageLookup.latitude").elementAt(0)).as("_package.latitude")
                .and(ArrayOperators.arrayOf("$packageLookup.brochure").elementAt(0)).as("_package.brochure")
                .and(ArrayOperators.arrayOf("$packageLookup.longtuid").elementAt(0)).as("_package.longtuid")
                .and(ArrayOperators.arrayOf("$packageLookup.latitudeTo").elementAt(0)).as("_package.latitudeTo")
                .and(ArrayOperators.arrayOf("$packageLookup.longtuidTo").elementAt(0)).as("_package.longtuidTo")
                .and(ArrayOperators.arrayOf("$packageLookup.medias").elementAt(0)).as("_package.medias")
                .and(ArrayOperators.arrayOf("$packageLookup.Place").elementAt(0)).as("_package.Place")
                .and(ArrayOperators.arrayOf("$packageLookup.slug").elementAt(0)).as("_package.slug")
                .and(ArrayOperators.arrayOf("$packageLookup.ToPlace").elementAt(0)).as("_package.ToPlace")
                .and(ArrayOperators.arrayOf("$packageLookup.rates").elementAt(0)).as("_package.rates")
                .and(ArrayOperators.arrayOf("$packageLookup.fromAirport").elementAt(0)).as("_package.fromAirport")
                .and(ArrayOperators.arrayOf("$packageLookup.toAirport").elementAt(0)).as("_package.toAirport")
                .and(ArrayOperators.arrayOf("$packageLookup.activities").elementAt(0)).as("_package.activities")
                .and(ArrayOperators.arrayOf("$packageLookup.availableForFollowMe").elementAt(0)).as("_package.availableForFollowMe")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("_package.infulancer._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("_package.infulancer.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("_package.infulancer.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$userLookup.follwerscount").elementAt(0)).as("_package.infulancer.follwerscount")
                .and(ArrayOperators.arrayOf("$packageLookup.description").elementAt(0)).as("_package.description")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$moodLookup")
                                .append("as", "mood")
                                .append("in", new Document("Title", "$$mood.Title")
                                        .append("_id", "$$mood._id")
                                        .append("type", "$$mood.type")
                                        .append("media", new Document()
                                                .append("type", "$$mood.media.type")
                                                .append("caption", "$$mood.media.caption")
                                                .append("asset", "$$mood.media.asset")
                                                .append("url", "$$mood.media.url")
                                        )
                                        .append("mediaIcon", new Document()
                                                .append("type", "$$mood.mediaIcon.type")
                                                .append("caption", "$$mood.mediaIcon.caption")
                                                .append("asset", "$$mood.mediaIcon.asset")
                                                .append("url", "$$mood.mediaIcon.url")
                                        )

                                )
                        );
                    }
                }).as("_package.moods")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$tagLookup")
                                .append("as", "tag")
                                .append("in", new Document("text", "$$tag.text")
                                        .append("_id", "$$tag._id")


                                )
                        );
                    }
                }).as("_package.tags")
        );

        return getGridAggregation(criteria, null, null, operations);


    }

    @Override
    public Aggregation getPostComments(Criteria criteria, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");

        LookupOperation postLookupOperation = LookupOperation.newLookup()
                .from("post")
                .localField("post.$id")
                .foreignField("_id")
                .as("postLookup");

        LookupOperation mentionsLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("mentions.$id")
                .foreignField("_id")
                .as("mentionLookup");

        operations.add(userLookupOperation);
        operations.add(postLookupOperation);
        operations.add(mentionsLookupOperation);
        operations.add(project("_id", "comment", "createdDate", "numberOfReactions", "numberOfReplyes")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$postLookup._id").elementAt(0)).as("post._id")
                .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$mentionLookup")
                                .append("as", "user")
                                .append("in", new Document()
                                        .append("_id", "$$user._id")
                                        .append("firstName", "$$user.firstName")
                                        .append("lastName", "$$user.lastName")
                                        .append("username", "$$user.username")
                                        .append("usertype", "$$user.usertype")
                                        .append("profileImage", "$$user.profileImage")
                                )
                        );
                    }
                }).as("mentions")
        );
        if (criteria != null) {
            operations.add(match(criteria));
        }

        return getGridAggregation(criteria, pageable, null, operations);
    }

    @Override
    public Aggregation getMediaComments(Criteria criteria, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();

        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("user.$id")
                .foreignField("_id")
                .as("userLookup");

        LookupOperation postLookupOperation = LookupOperation.newLookup()
                .from("media")
                .localField("media.$id")
                .foreignField("_id")
                .as("mediaLookup");

        LookupOperation mentionsLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("mentions.$id")
                .foreignField("_id")
                .as("mentionLookup");

        operations.add(userLookupOperation);
        operations.add(postLookupOperation);
        operations.add(mentionsLookupOperation);
        operations.add(project("_id", "comment", "createdDate", "numberOfReactions", "numberOfReplyes")
                .and(ArrayOperators.arrayOf("$userLookup._id").elementAt(0)).as("user._id")
                .and(ArrayOperators.arrayOf("$userLookup.firstName").elementAt(0)).as("user.firstName")
                .and(ArrayOperators.arrayOf("$userLookup.lastName").elementAt(0)).as("user.lastName")
                .and(ArrayOperators.arrayOf("$userLookup.username").elementAt(0)).as("user.username")
                .and(ArrayOperators.arrayOf("$userLookup.coverImage").elementAt(0)).as("user.coverImage")
                .and(ArrayOperators.arrayOf("$userLookup.profileImage").elementAt(0)).as("user.profileImage")
                .and(ArrayOperators.arrayOf("$userLookup.usertype").elementAt(0)).as("user.usertype")
                .and(ArrayOperators.arrayOf("$mediaLookup._id").elementAt(0)).as("media._id")
                 .and(new AggregationExpression() {
                    @Override
                    public Document toDocument(AggregationOperationContext context) {
                        return new Document("$map", new Document()
                                .append("input", "$mentionLookup")
                                .append("as", "user")
                                .append("in", new Document()
                                        .append("_id", "$$user._id")
                                        .append("firstName", "$$user.firstName")
                                        .append("lastName", "$$user.lastName")
                                        .append("username", "$$user.username")
                                        .append("usertype", "$$user.usertype")
                                        .append("profileImage", "$$user.profileImage")
                                )
                        );
                    }
                }).as("mentions")
        );
        if (criteria != null) {
            operations.add(match(criteria));
        }

        return getGridAggregation(criteria, pageable, null, operations);
    }


    /**
     * Builds an aggregation pipeline for MongoDB search queries with optional filtering and pagination.
     *
     * @param query     The search query string. If null or empty, no specific query filter is applied.
     * @param classmate The class name to filter results by. Expected format: fully qualified class name (e.g., "com.hb.core.searchPost").
     *                  If null or empty, no class-based filtering is applied.
     * @param pageable  Pageable object for controlling pagination and sorting.
     * @return An {@link Aggregation} The aggregation pipeline for performing the search operation.
     */


    @Override
    public Aggregation getSearch(String query, SearchEnum classmate, Pageable pageable, Criteria criteria) {

        PackageType packageTypeFilter= null;

        if(classmate!=null && classmate.equals(SearchEnum.Package))
            packageTypeFilter=PackageType.TravelWithMe;

        if(classmate!=null && classmate.equals(SearchEnum.followPackage))
            packageTypeFilter=PackageType.FollowMe;

        List<AggregationOperation> operations = new ArrayList<>();
        String classFilter = classmate!=null  ? extractClass(classmate) :"";

        // Check if both query and classFilter are null or empty
        if ((query == null || query.isEmpty()) && (classFilter == null || classFilter.isEmpty())) {
            // If both are not provided, fetch all documents
            return getGridAggregation(criteria, pageable, pageable.getSort(), operations);
        }


        // Create the $search stage
        AggregationOperation searchStage = new AggregationOperation() {
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {

                List<Document> mustClauses = new ArrayList<>();

                // Add _class filter if provided
                if (classFilter != null && !classFilter.isEmpty()) {
                    mustClauses.add(new Document()
                            .append("equals", new Document()
                                    .append("value", classFilter)
                                    .append("path", "_class")));
                }

                // Add text search if query is provided
                if (query != null && !query.isEmpty()) {
                    List<Document> shouldClauses = new ArrayList<>();

                    // Primary fields (text, fuzzy)
                    shouldClauses.add(buildSearchClause(query, Arrays.asList(
                            "text" , "name","slug"
                    ), 10, true, "text"));

                    shouldClauses.add(buildSearchClause(query, List.of("Package.name","Package.slug"
                            ,"infulancer.firstName"
                            ,"infulancer.lastName","user.firstName","packagePlaces.countryName","packagePlaces.cities.cityName","packagePlaces.cities.areas.areaName"
                            , "user.lastName"), 5, true, "text"));


                    shouldClauses.add(buildSearchClause(query, List.of("description"), 10, false, "regex"));


                    // Description autocomplete
                    shouldClauses.add(buildSearchClause(query, List.of("Package.description"), 5, false, "regex"));

                    // Wildcard fallback (inline)
                    shouldClauses.add(new Document("text", new Document()
                            .append("query", query)
                            .append("path", new Document("wildcard", "*"))
                    ));

                    mustClauses.add(new Document("compound", new Document()
                            .append("should", shouldClauses)
                            .append("minimumShouldMatch", 1)));
                }


                return new Document("$search", new Document()
                        .append("index", "default")
                        .append("compound", new Document()
                                .append("must", mustClauses)));
            }
        };

        operations.add(searchStage);

        // Add additional filters using $match stage
        if ("com.hb.crm.core.searchBeans.searchPackage".equals(classFilter)) {
            if (packageTypeFilter != null) {
                if (packageTypeFilter == PackageType.TravelWithMe) {
                    operations.add(
                            match(
                                    new Criteria().orOperator(
                                            Criteria.where("state").is(0),
                                            Criteria.where("state").is(3)
                                    )
                            )
                    );
                }
                operations.add(match(Criteria.where("packageType").is(packageTypeFilter)));
            }
            operations.add(match(Criteria.where("packageStatus").is(PackageStatus.posted)));
        }

        return getGridAggregation(criteria, pageable, pageable.getSort(), operations);
    }
    @Override
     public Aggregation getSearchMedia(String query, Pageable pageable, Criteria criteria){

        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(match(criteria));

        operations.add(Aggregation.lookup("user", "user.$id", "_id", "user"));
        // 4. $unwind user
        operations.add(Aggregation.unwind("user", true));
        // 5. $lookup tags
        operations.add(Aggregation.lookup("tag", "tags.$id", "_id", "tags"));
        // 6. $lookup post
        operations.add(Aggregation.lookup("post", "post.$id", "_id", "post"));
        // 7. $unwind post
        operations.add(Aggregation.unwind("post", true));
        // 8. $lookup package
        operations.add(Aggregation.lookup("package", "_package.$id", "_id", "_package"));
        // 9. $unwind package
        operations.add(Aggregation.unwind("_package", true));
        // 10. $project using AggregationExpression
        ProjectionOperation projectStage = Aggregation.project()
                .and("_id").as("id")
                .and("title").as("title")
                .and("creationDate").as("creationDate")
                .and("lastUpdate").as("lastUpdate")
                .and("source").as("source")
                .and("description").as("description")
                .and("videoUrl").as("videoUrl")
                .and("imageCategory").as("imageCategory")
                .and("videoDuration").as("videoDuration")
                .and("videoDurationMS").as("videoDurationMS")
                .and("thumbnailClipUrl").as("thumbnailClipUrl")
                .and("thumbnailCaptureUrl").as("thumbnailCaptureUrl")
                .and("mediaType").as("mediaType")
                .and("OwnerId").as("OwnerId")
                .and("videoSize").as("videoSize")
                .and("LastUpdaterId").as("LastUpdaterId")
                .and("employee").as("employee")
                .and("numberOfReactions").as("numberOfReactions")
                .and("numberOfComments").as("numberOfComments")
                // user
                .and(context -> new Document("_id", new Document("$toString", "$user._id"))
                        .append("profileImage", "$user.profileImage")
                        .append("firstName", "$user.firstName")
                        .append("lastName", "$user.lastName")
                        .append("about", "$user.about")
                        .append("username", "$user.username")).as("user")
                // tags
                .and(context -> new Document("$map", new Document("input", "$tags")
                        .append("as", "tag")
                        .append("in", new Document("id", new Document("$toString", "$$tag._id"))
                                .append("text", "$$tag.text")))).as("tags")
                // postOrStory
                .and(context -> new Document("postOrStory_id", new Document("$toString", "$post._id"))
                        .append("text", "$post.text")
                        .append("slug", "$post.slug")
                        .append("description", "$post.description")
                        .append("type", "$post.type")).as("postOrStory")

                // _package
                .and(context ->
                        new Document("_id", "$_package._id")
                        .append("name", "$_package.name")
                        .append("slug", "$_package.slug")
                        .append("description", "$_package.description")).as("package");

        operations.add(projectStage);
        // Create the $search stage
        AggregationOperation searchStage = new AggregationOperation() {
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {
                if (query == null || query.isEmpty()) {
                    return new Document(); // No filter
                }

                List<Document> orConditions = new ArrayList<>();

                // Basic match on common fields with case-insensitive regex
                List<String> fields = Arrays.asList(
                        "text", "name", "slug",
                        "_package.name", "_package.slug", "_package.description",
                        "user.firstName", "user.lastName"
                );

                for (String field : fields) {
                    orConditions.add(new Document(field, new Document("$regex", query).append("$options", "i")));
                }

                // Wildcard-like fallback is just additional regex on many fields, already covered

                return new Document("$match", new Document("$or", orConditions));
            }
        };

        operations.add(searchStage);

        return getGridAggregation(null, pageable, pageable.getSort(), operations);
    }

    private Document buildSearchClause(String query, List<String> paths, int boostValue, boolean fuzzy, String type) {
        Document clause = new Document("query", query);

        switch (type) {
            case "text":
                clause.append("path", paths);
                if (fuzzy) {
                    clause.append("fuzzy", new Document()
                            .append("maxEdits", 2)
                            .append("prefixLength", 0)
                            .append("maxExpansions", 50));
                }
                break;

            case "regex":
                if (paths.size() != 1) {
                    throw new IllegalArgumentException("Regex supports only one path");
                }
                clause.append("path", paths.get(0))
                        .append("allowAnalyzedField", true);
                break;

            default:
                throw new IllegalArgumentException("Unsupported search type: " + type);
        }

        if (boostValue > 0) {
            clause.append("score", new Document("boost", new Document("value", boostValue)));
        }

        return new Document(type, clause);
    }


    @Override
    public Aggregation getTaggedReel(Pageable pageable, Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();
         // "tags" is the field in media, referencing tag._id
                operations.add(Aggregation.lookup("tag", "tags.$id", "_id", "tagObjects"));

        // If both are not provided, fetch all documents
            return getGridAggregation(criteria, pageable, pageable.getSort(),operations);

    }
    private String extractClass(SearchEnum classFilter) {
        if (classFilter.equals(SearchEnum.Package))
            return "com.hb.crm.core.searchBeans.searchPackage";
        if (classFilter.equals(SearchEnum.followPackage))
            return "com.hb.crm.core.searchBeans.searchPackage";
        else if (classFilter.equals(SearchEnum.post))
            return "com.hb.crm.core.searchBeans.searchPost";
        else if (classFilter.equals(SearchEnum.story))
            return "com.hb.crm.core.searchBeans.searchStory";
        else
            return null;
    }


    @Override
    public Aggregation getSearchInfluencer(String query, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Check if both query are null or empty, fetch all documents
        if ((query == null || query.isEmpty()))
            return getGridAggregation(Criteria.where("usertype").is(UserType.Influencer), pageable, pageable.getSort(), operations);

        // Create the $search stage
        AggregationOperation searchStage1 = matchQuery(query);
        operations.add(searchStage1);

        return getGridAggregation(Criteria.where("usertype").is(UserType.Influencer), pageable, pageable.getSort(), operations);
    }

    @Override
    public Aggregation getSearchInfluencerScored(Criteria criteria ,String query, List<String> moodTitles,
                                                 List<ObjectId> followedInfluencers, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Check if query is not null or empty, search for it
        if ((query != null && !query.isEmpty())) {
            // Create the $search stage
            AggregationOperation searchStage1 = matchQuery(query);
            operations.add(searchStage1);
        }

        scoreSearchInfluencer(operations, moodTitles, followedInfluencers);

        // Sort by score DESC, followers count DESC
        operations.add(sort(Sort.by(
                Sort.Order.desc("finalScore"),
                Sort.Order.desc("follwerscount")
        )));

        return getGridAggregation(Criteria.where("usertype").is(UserType.Influencer), pageable, null, operations);
    }
    @Override
    public    Aggregation getSubscribePackageByID(Criteria criteria ) {
        List<AggregationOperation> operations = new ArrayList<>();
        LookupOperation userLookupOperation = LookupOperation.newLookup()
                .from("user")
                .localField("_id.user.$id")
                .foreignField("_id")
                .as("userLookup");
        LookupOperation subBackageLookup = LookupOperation.newLookup()
                .from("subPackage")
                .localField("_id._package.$id")
                .foreignField("_id")
                .as("subPackageLookup");
        operations.add(userLookupOperation);
        operations.add(subBackageLookup);
        // Add the traveler count using $size

        operations.add(project("total","expireDate","status","travelers"
                ,"RoomReservations","hotels","flights","confirmPriceRefraceId","flgihtCabins","numberOfRooms","adultCount","childCount",
                "documents","bockingDetailsUrl","departureFlight","destinationFlight","preferences","otherHotelPreferences","otherFlightPreferences","updateDate")
                .and(ArrayOperators.arrayOf("userLookup._id").elementAt(0)).as("_id.user._id")
                .and(ArrayOperators.arrayOf("subPackageLookup._id").elementAt(0)).as("_id._package._id")
                .and(ArrayOperators.arrayOf("userLookup.firstName").elementAt(0)).as("_id.user.firstName")
                .and(ArrayOperators.arrayOf("userLookup.lastName").elementAt(0)).as("_id.user.lastName")
                .and(ArrayOperators.arrayOf("userLookup.firstName").elementAt(0)).as("mainTravelerFirstName")
                .and(ArrayOperators.arrayOf("userLookup.lastName").elementAt(0)).as("mainTravelerLastName")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.mobile").elementAt(0)).as("_id.user.userInfo.mobile")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.email").elementAt(0)).as("_id.user.userInfo.email")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.mobile").elementAt(0)).as("mainTravelerPhoneNumber")
                .and(ArrayOperators.arrayOf("userLookup.userInfo.email").elementAt(0)).as("mainTravelerEmail")
                .and(ArrayOperators.arrayOf("userLookup.profileImage").elementAt(0)).as("_id.user.profileImage")
        );
        // Add the traveler count using $size
        if (criteria != null) {
            operations.add(match(criteria));
        }

        return  newAggregation(operations);
    }
    @Override
    public Aggregation GetRecommendedInfluencers(Criteria criteria, List<String> userMoods,
                                                 List<ObjectId> followedInfluencers, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();



        operations.add(AddFieldsOperation.builder()
                .addField("moodTitlesArray")
                .withValue(
                        (AggregationExpression) context -> new Document("$map", new Document()
                                .append("input", new Document("$ifNull", Arrays.asList("$moods", Collections.emptyList())))
                                .append("as", "mood")
                                .append("in", "$$mood.Title"))
                ).build());

        operations.add(getMatchedMoods(userMoods));
        operations.add(influencerMatch(followedInfluencers));


        // Filter out matched influencers (only return NOT matched ones)
        operations.add(match(Criteria.where("influencerMatch").is(false)));
        operations.add(calculateFinalScore(userMoods.size()));

        // Sort by score DESC, followers count DESC
        operations.add(sort(Sort.by(
                Sort.Order.desc("finalScore"),
                Sort.Order.desc("follwerscount")
        )));

        return getGridAggregation( criteria, pageable, null, operations);
    }



    public void scoreSearchInfluencer(List<AggregationOperation> operations, List<String> moodTitles,
                                      List<ObjectId> followedInfluencers) {
        operations.add(AddFieldsOperation.builder()
                .addField("moodTitlesArray")
                .withValue(
                        (AggregationExpression) context -> new Document("$map", new Document()
                                .append("input", new Document("$ifNull", Arrays.asList("$moods", Collections.emptyList())))
                                .append("as", "mood")
                                .append("in", "$$mood.Title"))
                ).build());

        operations.add(getMatchedMoods(moodTitles));
        operations.add(influencerMatch(followedInfluencers));
        operations.add(calculateFinalScore(moodTitles.size()));
    }

    private AggregationOperation getMatchedMoods(List<String> userMoodNames) {
        return aggregationOperationContext -> new Document("$addFields",
                new Document("matchedMoodCount",
                        new Document("$size",
                                new Document("$setIntersection",
                                        Arrays.asList("$moodTitlesArray", userMoodNames)
                                )
                        )
                )
        );
    }

    private AggregationOperation influencerMatch(List<ObjectId> influencerIds) {
        return AddFieldsOperation.builder()
                .addField("influencerMatch")
                .withValue(
                        new AggregationExpression() {
                            @NotNull
                            @Override
                            public Document toDocument(@NotNull AggregationOperationContext context) {
                                return new Document(
                                        "$in",
                                        Arrays.asList("$_id", influencerIds)
                                );
                            }
                        }
                )
                .build();
    }




    private AggregationOperation calculateFinalScore(int userMoodCount) {
        return AddFieldsOperation.builder()
                .addField("finalScore")
                .withValue(
                        (AggregationExpression) aggregationOperationContext -> new Document("$cond", Arrays.asList(
                                "$influencerMatch", // If influencer is matched
                                6,                  // Set final score to 6
                                new Document("$cond", Arrays.asList(
                                        new Document("$gt", Arrays.asList(userMoodCount, 0)), // Ensure no division by zero
                                        new Document("$multiply", Arrays.asList(
                                                new Document("$divide", Arrays.asList("$matchedMoodCount",
                                                        new Document("$literal", userMoodCount))),
                                                5.0 // Scale to max 5
                                        )),
                                        0 // Default score if influencerMoodCount is 0
                                ))
                        ))
                ).build();
    }


    @Override
    public Aggregation getSearchUsers(Criteria criteria, String query, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Check if both query are null or empty, fetch all documents
        if ((query == null || query.isEmpty()))
            return getGridAggregation(criteria, pageable, pageable.getSort(), operations);

        // Create the $search stage
        AggregationOperation searchStage1 = matchQuery(query);
        operations.add(searchStage1);
        return getGridAggregation(criteria, pageable, pageable.getSort(), operations);
    }

    private AggregationOperation matchQuery(String query) {
        AggregationOperation searchStage = new AggregationOperation() {

            /**
             * Dynamically constructs a $search stage for MongoDB's aggregation pipeline.
             * Supports text-based filtering.
             *
             * @param context The aggregation operation context for the current stage.
             * @return Document The MongoDB aggregation $search stage.
             */
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {
                return new Document("$search", new Document()
                        .append("index", "default") // Your search index name
                        .append("wildcard", new Document() // 🔹 Changed from "compound" to "wildcard"
                                .append("query", "*" + query + "*") // 🔹 Equivalent to SQL LIKE '%query%'
                                .append("path", new Document("wildcard", "*")) // 🔹 Search in all fields
                                .append("allowAnalyzedField", true))); // 🔹 Allows searching in analyzed fields
            }
        };

        return searchStage;
    }

    @NotNull
    private Aggregation getGridAggregation2(Criteria criteria, Pageable pageable, Sort sort, boolean count, List<AggregationOperation> operations) {
        if (criteria != null) {
            operations.add(Aggregation.match(criteria));
        }
        if (count) {
            operations.add(Aggregation.group().count().as("count"));
        } else {
            if (sort != null)
                operations.add(Aggregation.sort(sort));
            if (pageable != null) {
                operations.add(Aggregation.skip(pageable.getOffset()));
                operations.add(Aggregation.limit(pageable.getPageSize()));
            }

        }

        return newAggregation(operations);
    }

    @Override
    public Aggregation explorePackages(String searchTerm, Pageable pageable) {
        List<AggregationOperation> operations = new ArrayList<>();

        // Add _class filter first
        operations.add(match(Criteria.where("_class").is("com.hb.crm.core.searchBeans.searchPackage")));

        if(searchTerm == null || searchTerm.isEmpty()){
            return getGridAggregation(null, pageable, pageable.getSort(), operations);
        }

        // Unwind packagePlaces
        operations.add(unwind("packagePlaces"));

        // Unwind cities
        operations.add(unwind("packagePlaces.cities"));

        // Add lookups
        LookupOperation countryLookup = LookupOperation.newLookup()
                .from("country")
                .localField("packagePlaces.$propertyId")
                .foreignField("_id")
                .as("countries");

        LookupOperation cityLookup = LookupOperation.newLookup()
                .from("city")
                .localField("packagePlaces.cities.$propertyId")
                .foreignField("_id")
                .as("cities");

        operations.add(countryLookup);
        operations.add(cityLookup);

        // Add search criteria
        Criteria searchCriteria = new Criteria().orOperator(
                Criteria.where("name").regex(searchTerm, "i"),
                Criteria.where("description").regex(searchTerm, "i"),
                Criteria.where("infulancer.name").regex(searchTerm, "i"),
                Criteria.where("countries.name").regex(searchTerm, "i"),
                Criteria.where("cities.name").regex(searchTerm, "i")
        );

        operations.add(match(searchCriteria));

        return getGridAggregation(null, pageable, pageable.getSort(), operations);
    }

    public Aggregation searchByNameOrPackage(String query, int skip, int limit) {
        // Build all aggregation stages
        List<AggregationOperation> aggregationOperations = Arrays.asList(
                buildUserSearchStage(query),
                buildUnionWithStage(query),
                buildGroupStage(),
                buildReplaceRootStage(),
                Aggregation.match(Criteria.where("usertype").in(UserType.Traveler, UserType.Influencer)),
                buildFacetStage(skip, limit),
                buildProjectStage()
        );

        // Execute aggregation
        return Aggregation.newAggregation(aggregationOperations);
    }

    public AggregationOperation buildUserSearchStage(String query) {

        return new AggregationOperation() {
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {
                return new Document("$search", new Document()
                        .append("index", "default")
                        .append("compound", new Document()
                                .append("should", Arrays.asList(
                                        new Document("wildcard", new Document()
                                                .append("path", "firstName")
                                                .append("query", "*" + query + "*")
                                                .append("allowAnalyzedField", true)),
                                        new Document("wildcard", new Document()
                                                .append("path", "lastName")
                                                .append("query", "*" + query + "*")
                                                .append("allowAnalyzedField", true))
                                ))
                        ));
            }
        };
    }

    public AggregationOperation buildUnionWithStage(String query) {
        // Package search pipeline inside unionWith
        return new AggregationOperation() {

            // Package search pipeline inside unionWith
            final Document packageSearch = new Document("$search", new Document()
                    .append("index", "default")
                    .append("compound", new Document()
                            .append("must", Arrays.asList(
                                    new Document("equals", new Document()
                                            .append("path", "_class")
                                            .append("value", "com.hb.crm.core.searchBeans.searchPackage")),
                                    new Document("wildcard", new Document()
                                            .append("path", "name")
                                            .append("query", "*" + query + "*")
                                            .append("allowAnalyzedField", true))
                            ))
                    ));

            final List<Document> unionPipeline = Arrays.asList(
                    packageSearch,
                    new Document("$replaceRoot", new Document("newRoot", "$infulancer")),
                    new Document("$lookup", new Document()
                            .append("from", "searchUser")
                            .append("localField", "_id")
                            .append("foreignField", "_id")
                            .append("as", "completeUser")),
                    new Document("$unwind", new Document("path", "$completeUser")
                            .append("preserveNullAndEmptyArrays", false)),
                    new Document("$replaceRoot", new Document("newRoot", "$completeUser"))
            );

            final Document unionWithStage = new Document("$unionWith", new Document()
                    .append("coll", "search")
                    .append("pipeline", unionPipeline));

            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {
                return unionWithStage;
            }

        };
    }

    public AggregationOperation buildGroupStage() {
        return new AggregationOperation() {
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {
                return new Document("$group", new Document()
                        .append("_id", "$_id")
                        .append("doc", new Document("$first", "$$ROOT")));
            }
        };
    }

    public AggregationOperation buildReplaceRootStage() {
        return new AggregationOperation() {
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {
                return new Document("$replaceRoot", new Document("newRoot", "$doc"));
            }
        };
    }

    public AggregationOperation buildFacetStage(int skip, int limit) {
        return new AggregationOperation() {
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {
                return new Document("$facet", new Document()
                        .append("filteredResults", Arrays.asList(
                                new Document("$skip", skip),
                                new Document("$limit", limit)
                        ))
                        .append("totalCount", Arrays.asList(
                                new Document("$count", "total")
                        )));
            }
        };
    }

    public AggregationOperation buildProjectStage() {
        return new AggregationOperation() {
            @NotNull
            @Override
            public Document toDocument(@NotNull AggregationOperationContext context) {
                return new Document("$project", new Document()
                        .append("filteredResults", 1)
                        .append("totalCount", new Document()
                                .append("$cond", new Document()
                                        .append("if", new Document("$eq", Arrays.asList(
                                                new Document("$size", "$totalCount"), 0)))
                                        .append("then", 0)
                                        .append("else", new Document("$arrayElemAt",
                                                Arrays.asList("$totalCount.total", 0)))
                                )));
            }
        };
    }

    public com.hb.crm.core.dtos.searchUserResultDto mapToSearchUserResultDto(Document document) {
        com.hb.crm.core.dtos.searchUserResultDto dto = new com.hb.crm.core.dtos.searchUserResultDto();
        if (document != null) {
            dto.setFilteredResults(new ArrayList<>(document.getList("filteredResults", SearchUser.class)));
            dto.setTotalCount(document.getInteger("totalCount"));
        }
        return dto;
    }




}
