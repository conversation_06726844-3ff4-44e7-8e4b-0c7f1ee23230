package com.hb.crm.client.services;

import com.hb.crm.client.CommonService.CommonUtils;
import com.hb.crm.client.CommonService.Mapper;
import com.hb.crm.client.CommonService.SpectrumCommon;
import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.config.DateFormatter;
import com.hb.crm.client.dto.CreateUpdatePackageWithMediaWrapperDto;
import com.hb.crm.core.dtos.CreateUpdatePackageDto;
import com.hb.crm.client.services.interfaces.*;
import com.hb.crm.core.CashService.CashService;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.Package;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.repositories.chat.GroupConversationRepository;
import com.hb.crm.core.searchBeans.searchPackage;
import com.hb.crm.core.searchRepositories.ReactionSearchRepository;
import com.hb.crm.core.searchRepositories.SearchPackageRepository;
import com.hb.crm.core.services.chat.ChatMessageService;
import com.hb.crm.core.services.interfaces.NotificationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for PackageServiceImpl.update() method.
 * Tests both happy scenarios: new package creation and existing package update.
 */
@ExtendWith(MockitoExtension.class)
public class PackageServiceImplTest {

    @Mock
    private PackageRepository packageRepository;

    @Mock
    private SubPackageRepository subPackageRepository;

    @Mock
    private SearchPackageRepository searchPackageRepository;

    @Mock
    private MediaRepository mediaRepository;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private UserSessionData userSessionData;

    @Mock
    private QueryNormalizeService queryNormalazieService;

    @Mock
    private Mapper mapper;

    @Mock
    private AggregationResults<User> aggregationResults;

    // Additional required dependencies for PackageServiceImpl constructor
    @Mock
    private UserRepository userRepository;

    @Mock
    private SubscribeRepository subscribeRepository;

    @Mock
    private PackageNotificationRepository packageNotificationRepository;

    @Mock
    private PackageReactionRepository packageReactionRepository;

    @Mock
    private MongoTemplate searchmongoTemplate;

    // Additional mocks for other dependencies
    @Mock
    private ActivityCloneRepository activityCloneRepository;

    @Mock
    private SettingService settingService;

    @Mock
    private Spectrum spectrum;

    @Mock
    private StripePaymentService paymentService;

    @Mock
    private CashService cashService;

    @Mock
    private AtlasService atlasService;

    @Mock
    private SpectrumCommon spectrumCommon;

    @Mock
    private MediaService mediaService;

    // Additional missing dependencies from constructor
    @Mock
    private CountryRepository countryRepository;

    @Mock
    private CityRepository cityRepository;

    @Mock
    private AreaRepository areaRepository;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private ActivityRepository activityRepository;

    @Mock
    private ActivityCategoryRepository activityCategoryRepository;

    @Mock
    private SearchService searchService;

    @Mock
    private TravelerSubscribeRepository travelerSubscribeRepository;

    @Mock
    private ReactionSearchRepository reactionSearchRepository;

    @Mock
    private ChatMessageService chatMessageService;

    @Mock
    private GroupConversationRepository groupConversationRepository;

    @Mock
    private NotificationService notificationService;

    @Mock
    private PackageNotificationSettingRepository packageNotificationSettingRepository;

    @Mock
    private ModelMapper modelMapper;

    // Create a partial mock to test specific methods
    private PackageServiceImpl packageService;

    private CreateUpdatePackageDto createPackageDto;
    private CreateUpdatePackageDto updatePackageDto;
    private CreateUpdatePackageWithMediaWrapperDto packageWithMediaDto;
    private Package mockPackage;
    private SubPackage mockSubPackage;
    private SubPackage existingSubPackage;
    private User mockUser;
    private searchPackage mockSearchPackage;
    private Aggregation mockAggregation;
    private TagService tagService;

    @BeforeEach
    void setUp() {
        // Create a spy of the service with mocked dependencies
        packageService = spy(new PackageServiceImpl(
                packageRepository,
                userRepository,
                subscribeRepository,
                subPackageRepository,
                packageNotificationRepository,
                packageReactionRepository,
                mongoTemplate,
                searchmongoTemplate,
                userSessionData,
                mediaService,
                activityCloneRepository,
                settingService,
                spectrum,
                paymentService,
                cashService,
                queryNormalazieService,
                atlasService,
                spectrumCommon,
                countryRepository,
                cityRepository,
                areaRepository,
                commonUtils,
                mapper,
                searchPackageRepository,
                activityRepository,
                activityCategoryRepository,
                searchService,
                mediaRepository,
                travelerSubscribeRepository,
                reactionSearchRepository,
                chatMessageService,
                groupConversationRepository,
                notificationService,
                packageNotificationSettingRepository,
                modelMapper,
                tagService
        ));

        // Setup test data for new package creation
        createPackageDto = new CreateUpdatePackageDto();
        createPackageDto.setId(null); // null ID indicates new package
        createPackageDto.setName("Test Package");
        createPackageDto.setDescription("Test Description");
        createPackageDto.setStart(LocalDateTime.now().plusDays(1));
        //createPackageDto.setDuration(7);

        // Setup test data for package update
        updatePackageDto = new CreateUpdatePackageDto();
        updatePackageDto.setId("507f1f77bcf86cd799439015"); // Valid 24-character ObjectId (different from others)
        updatePackageDto.setName("Updated Package");
        updatePackageDto.setDescription("Updated Description");
        updatePackageDto.setStart(LocalDateTime.now().plusDays(2));
        //updatePackageDto.setDuration(10);

        // Setup mock entities with valid ObjectId strings (24 hex characters)
        mockUser = new User();
        mockUser.setId("507f1f77bcf86cd799439011"); // Valid 24-character ObjectId
        mockUser.setFirstName("Test");
        mockUser.setLastName("User");

        mockPackage = new Package();
        mockPackage.setId("507f1f77bcf86cd799439012"); // Valid 24-character ObjectId
        mockPackage.setName("Test Package");
        mockPackage.setInfulancer(mockUser);
        mockPackage.setMedias(new ArrayList<>());

        mockSubPackage = new SubPackage();
        mockSubPackage.setId("507f1f77bcf86cd799439013"); // Valid 24-character ObjectId
        mockSubPackage.setName("Test Package");
        mockSubPackage.setPackageType(PackageType.TravelWithMe);
        mockSubPackage.set_package(mockPackage);

        existingSubPackage = new SubPackage();
        existingSubPackage.setId("507f1f77bcf86cd799439015"); // Valid 24-character ObjectId (matches updatePackageDto)
        existingSubPackage.setName("Existing Package");
        existingSubPackage.setPackageType(PackageType.TravelWithMe);
        existingSubPackage.set_package(mockPackage);
        existingSubPackage.setCreationDate(LocalDateTime.now().minusDays(5));
        existingSubPackage.setPackageStatus(com.hb.crm.core.Enums.PackageStatus.draft);
        existingSubPackage.setRejectionNote("");
        existingSubPackage.setStart(LocalDateTime.now().plusDays(1));
        existingSubPackage.setEnd(LocalDateTime.now().plusDays(8));
        existingSubPackage.setState(com.hb.crm.core.Enums.State.NotStarted);

        packageWithMediaDto = new CreateUpdatePackageWithMediaWrapperDto(createPackageDto);

        mockSearchPackage = new searchPackage();
        mockSearchPackage.setName("Test Package");

        mockAggregation = mock(Aggregation.class);
    }

    /**
     * Test successful creation of a new package (happy scenario).
     * Verifies that all required operations are performed correctly when creating a new package.
     */
    @Test
    void testUpdate_NewPackageCreation_Success() {
        // Arrange
        try (MockedStatic<DateFormatter> dateFormatterMock = mockStatic(DateFormatter.class)) {
            LocalDateTime formattedDateTime = LocalDateTime.now();
            dateFormatterMock.when(() -> DateFormatter.formatLocalDateTime(any(LocalDateTime.class)))
                    .thenReturn(formattedDateTime);

            // Mock user session and aggregation
            when(userSessionData.getId()).thenReturn("67f50d3b92d8152941ad90a5");
            when(queryNormalazieService.getUserFields(any(Criteria.class))).thenReturn(mockAggregation);
            when(mongoTemplate.aggregate(eq(mockAggregation), eq("user"), eq(User.class)))
                    .thenReturn(aggregationResults);
            when(aggregationResults.getMappedResults()).thenReturn(List.of(mockUser));

            // Mock entity mapping directly from DTO
            when(mapper.convertToEntity((CreateUpdatePackageDto) any())).thenReturn(mockPackage);
            when(mapper.convertToSubEntity((CreateUpdatePackageDto) any())).thenReturn(mockSubPackage);

            // Mock repository operations
            when(packageRepository.save(mockPackage)).thenReturn(mockPackage);
            when(subPackageRepository.save(mockSubPackage)).thenReturn(mockSubPackage);
            when(mapper.convertPackageToSearchPackage(mockSubPackage)).thenReturn(mockSearchPackage);
            when(searchPackageRepository.save(mockSearchPackage)).thenReturn(mockSearchPackage);

            // Mock the SubscribePackage method
            doNothing().when(packageService).SubscribePackage(anyString());

            // Act
            packageService.update(createPackageDto);

            // Assert
            // Verify user retrieval
            verify(queryNormalazieService).getUserFields(any(Criteria.class));
            verify(mongoTemplate).aggregate(eq(mockAggregation), eq("user"), eq(User.class));

            // Verify entity mapping
            verify(mapper).convertToEntity((CreateUpdatePackageDto) any());
            verify(mapper).convertToSubEntity((CreateUpdatePackageDto) any());

            // Verify that the package influencer is set correctly (check the actual object state)
            assertEquals(mockUser, mockPackage.getInfulancer());

            // Verify that timestamps are set for new package (check the actual object state)
            assertNotNull(mockSubPackage.getCreationDate());
            assertNotNull(mockSubPackage.getUpdateDate());

            // Verify package relationship is established (check the actual object state)
            assertEquals(mockPackage, mockSubPackage.get_package());

            // Verify repository saves
            verify(packageRepository).save(mockPackage);
            verify(subPackageRepository).save(mockSubPackage);

            // Verify search index update
            verify(mapper).convertPackageToSearchPackage(mockSubPackage);
            verify(searchPackageRepository).save(mockSearchPackage);

            // Verify auto-subscription
            verify(packageService).SubscribePackage(mockSubPackage.getId());

            // Verify date formatting calls
            dateFormatterMock.verify(() -> DateFormatter.formatLocalDateTime(createPackageDto.getStart()));
            dateFormatterMock.verify(() -> DateFormatter.formatLocalDateTime(any(LocalDateTime.class)), atLeast(2));
        }
    }

    /**
     * Test successful update of an existing package (happy scenario).
     * Verifies that existing package data is preserved and only allowed fields are updated.
     */
    @Test
    void testUpdate_ExistingPackageUpdate_Success() {
        // Arrange
        try (MockedStatic<DateFormatter> dateFormatterMock = mockStatic(DateFormatter.class)) {
            LocalDateTime formattedDateTime = LocalDateTime.now();
            dateFormatterMock.when(() -> DateFormatter.formatLocalDateTime(any(LocalDateTime.class)))
                    .thenReturn(formattedDateTime);

            // Mock user session and aggregation
            when(userSessionData.getId()).thenReturn("507f1f77bcf86cd799439011");
            when(queryNormalazieService.getUserFields(any(Criteria.class))).thenReturn(mockAggregation);
            when(mongoTemplate.aggregate(eq(mockAggregation), eq("user"), eq(User.class)))
                    .thenReturn(aggregationResults);
            when(aggregationResults.getMappedResults()).thenReturn(List.of(mockUser));

            // Mock entity mapping directly from DTO
            when(mapper.convertToEntity((CreateUpdatePackageDto) any())).thenReturn(mockPackage);
            when(mapper.convertToSubEntity((CreateUpdatePackageDto) any())).thenReturn(mockSubPackage);

            // Mock existing package retrieval
            when(subPackageRepository.findById("507f1f77bcf86cd799439015"))
                    .thenReturn(Optional.of(existingSubPackage));

            // Mock repository operations
            when(packageRepository.save(mockPackage)).thenReturn(mockPackage);
            when(subPackageRepository.save(mockSubPackage)).thenReturn(mockSubPackage);
            when(mapper.convertPackageToSearchPackage(mockSubPackage)).thenReturn(mockSearchPackage);
            when(searchPackageRepository.save(mockSearchPackage)).thenReturn(mockSearchPackage);

            // Act
            packageService.update(updatePackageDto);

            // Assert
            // Verify user retrieval
            verify(queryNormalazieService).getUserFields(any(Criteria.class));
            verify(mongoTemplate).aggregate(eq(mockAggregation), eq("user"), eq(User.class));

            // Verify existing package retrieval
            verify(subPackageRepository).findById("507f1f77bcf86cd799439015");

            // Verify entity mapping
            verify(mapper).convertToEntity((CreateUpdatePackageDto) any());
            verify(mapper).convertToSubEntity((CreateUpdatePackageDto) any());

            // Verify preserved fields are set from existing package (check actual object state)
            assertEquals(existingSubPackage.getCreationDate(), mockSubPackage.getCreationDate());
            assertEquals(existingSubPackage.getPackageStatus(), mockSubPackage.getPackageStatus());
            assertEquals(existingSubPackage.getRejectionNote(), mockSubPackage.getRejectionNote());
            assertEquals(existingSubPackage.getState(), mockSubPackage.getState());

            // Verify update timestamp is set (check actual object state)
            assertNotNull(mockSubPackage.getUpdateDate());

            // Verify package relationship (check actual object state)
            assertEquals(mockPackage, mockSubPackage.get_package());

            // Verify repository saves
            verify(packageRepository).save(mockPackage);
            verify(subPackageRepository).save(mockSubPackage);

            // Verify search index update
            verify(mapper).convertPackageToSearchPackage(mockSubPackage);
            verify(searchPackageRepository).save(mockSearchPackage);

            // Verify auto-subscription is NOT called for updates
            verify(packageService, never()).SubscribePackage(anyString());

            // Verify date formatting calls
            dateFormatterMock.verify(() -> DateFormatter.formatLocalDateTime(updatePackageDto.getStart()));
            dateFormatterMock.verify(() -> DateFormatter.formatLocalDateTime(any(LocalDateTime.class)), atLeast(1));
        }
    }

    /**
     * Test package creation with media ownership update (happy scenario).
     * Verifies that media ownership is correctly updated when package has associated media.
     */
    @Test
    void testUpdate_NewPackageWithMedia_Success() {
        // Arrange
        try (MockedStatic<DateFormatter> dateFormatterMock = mockStatic(DateFormatter.class)) {
            LocalDateTime formattedDateTime = LocalDateTime.now();
            dateFormatterMock.when(() -> DateFormatter.formatLocalDateTime(any(LocalDateTime.class)))
                    .thenReturn(formattedDateTime);

            // Setup package with media
            User oldUser = new User();
            oldUser.setId("old-user-id");

            Media media1 = new Media();
            media1.setId("media-1");
            media1.setUser(oldUser);

            Media media2 = new Media();
            media2.setId("media-2");
            media2.setUser(oldUser);

            MediaWrapper wrapper1 = new MediaWrapper();
            wrapper1.setMedia(media1);

            MediaWrapper wrapper2 = new MediaWrapper();
            wrapper2.setMedia(media2);

            List<MediaWrapper> mediaWrappers = List.of(wrapper1, wrapper2);
            mockPackage.setMedias(mediaWrappers);

            // Mock user session and aggregation
            when(userSessionData.getId()).thenReturn("507f1f77bcf86cd799439011");
            when(queryNormalazieService.getUserFields(any(Criteria.class))).thenReturn(mockAggregation);
            when(mongoTemplate.aggregate(eq(mockAggregation), eq("user"), eq(User.class)))
                    .thenReturn(aggregationResults);
            when(aggregationResults.getMappedResults()).thenReturn(List.of(mockUser));

            // Mock entity mapping directly from DTO
            when(mapper.convertToEntity((CreateUpdatePackageDto) any())).thenReturn(mockPackage);
            when(mapper.convertToSubEntity((CreateUpdatePackageDto) any())).thenReturn(mockSubPackage);

            // Mock repository operations
            when(packageRepository.save(mockPackage)).thenReturn(mockPackage);
            when(subPackageRepository.save(mockSubPackage)).thenReturn(mockSubPackage);
            when(mapper.convertPackageToSearchPackage(mockSubPackage)).thenReturn(mockSearchPackage);
            when(searchPackageRepository.save(mockSearchPackage)).thenReturn(mockSearchPackage);
            when(mediaRepository.saveAll(anyList())).thenReturn(List.of(media1, media2));

            // Mock the SubscribePackage method
            doNothing().when(packageService).SubscribePackage(anyString());

            // Act
            packageService.update(createPackageDto);

            // Assert
            // Verify media ownership update
            verify(mediaRepository).saveAll(argThat(medias -> {
                List<Media> mediaList = (List<Media>) medias;
                return mediaList.size() == 2 &&
                       mediaList.stream().allMatch(media ->
                           media.getUser() != null && Objects.equals(media.getUser().getId(), mockUser.getId()));
            }));

            // Verify all other operations
            verify(packageRepository).save(mockPackage);
            verify(subPackageRepository).save(mockSubPackage);
            verify(searchPackageRepository).save(mockSearchPackage);
        }
    }

    /**
     * Test package update when user is null (edge case).
     * Verifies that the method handles gracefully when user authentication fails.
     */
    @Test
    void testUpdate_UserNotFound_NoOperationsPerformed() {
        // Arrange
        when(userSessionData.getId()).thenReturn("507f1f77bcf86cd799439011");
        when(queryNormalazieService.getUserFields(any(Criteria.class))).thenReturn(mockAggregation);
        when(mongoTemplate.aggregate(eq(mockAggregation), eq("user"), eq(User.class)))
                .thenReturn(aggregationResults);
        when(aggregationResults.getMappedResults()).thenReturn(List.of()); // Empty list = no user found

        // Mock entity mapping directly from DTO
        when(mapper.convertToEntity((CreateUpdatePackageDto) any())).thenReturn(mockPackage);
        when(mapper.convertToSubEntity((CreateUpdatePackageDto) any())).thenReturn(mockSubPackage);

        // Act
        packageService.update(createPackageDto);

        // Assert
        // Verify user retrieval was attempted
        verify(queryNormalazieService).getUserFields(any(Criteria.class));
        verify(mongoTemplate).aggregate(eq(mockAggregation), eq("user"), eq(User.class));

        // Verify DTO conversion still happens
        verify(mapper).convertToEntity((CreateUpdatePackageDto) any());
        verify(mapper).convertToSubEntity((CreateUpdatePackageDto) any());

        // Verify no repository operations are performed when user is null
        verify(packageRepository, never()).save(any());
        verify(subPackageRepository, never()).save(any());
        verify(searchPackageRepository, never()).save(any());
        verify(mediaRepository, never()).saveAll(any());
    }

    /**
     * Test package update with FollowMe type (edge case).
     * Verifies that FollowMe packages are not saved to the main package repository.
     */
    @Test
    void testUpdate_FollowMePackageType_PackageNotSaved() {
        // Arrange
        try (MockedStatic<DateFormatter> dateFormatterMock = mockStatic(DateFormatter.class)) {
            LocalDateTime formattedDateTime = LocalDateTime.now();
            dateFormatterMock.when(() -> DateFormatter.formatLocalDateTime(any(LocalDateTime.class)))
                    .thenReturn(formattedDateTime);

            // Setup FollowMe package type
            mockSubPackage.setPackageType(PackageType.FollowMe);

            // Mock user session and aggregation
            when(userSessionData.getId()).thenReturn("507f1f77bcf86cd799439011");
            when(queryNormalazieService.getUserFields(any(Criteria.class))).thenReturn(mockAggregation);
            when(mongoTemplate.aggregate(eq(mockAggregation), eq("user"), eq(User.class)))
                    .thenReturn(aggregationResults);
            when(aggregationResults.getMappedResults()).thenReturn(List.of(mockUser));

            // Mock entity mapping directly from DTO
            when(mapper.convertToEntity((CreateUpdatePackageDto) any())).thenReturn(mockPackage);
            when(mapper.convertToSubEntity((CreateUpdatePackageDto) any())).thenReturn(mockSubPackage);

            // Mock repository operations
            when(subPackageRepository.save(mockSubPackage)).thenReturn(mockSubPackage);
            when(mapper.convertPackageToSearchPackage(mockSubPackage)).thenReturn(mockSearchPackage);
            when(searchPackageRepository.save(mockSearchPackage)).thenReturn(mockSearchPackage);

            // Mock the SubscribePackage method
            doNothing().when(packageService).SubscribePackage(anyString());

            // Act
            packageService.update(createPackageDto);

            // Assert
            // Verify main package is NOT saved for FollowMe type
            verify(packageRepository, never()).save(mockPackage);

            // Verify sub-package and search index are still saved
            verify(subPackageRepository).save(mockSubPackage);
            verify(searchPackageRepository).save(mockSearchPackage);
        }
    }

    /**
     * Test package update with null influencer fallback (edge case).
     * Verifies that the current user is set as influencer when package influencer is null.
     */
    @Test
    void testUpdate_NullInfluencerFallback_Success() {
        // Arrange
        try (MockedStatic<DateFormatter> dateFormatterMock = mockStatic(DateFormatter.class)) {
            LocalDateTime formattedDateTime = LocalDateTime.now();
            dateFormatterMock.when(() -> DateFormatter.formatLocalDateTime(any(LocalDateTime.class)))
                    .thenReturn(formattedDateTime);

            // Setup package with null influencer
            mockPackage.setInfulancer(null);

            // Mock user session and aggregation
            when(userSessionData.getId()).thenReturn("507f1f77bcf86cd799439011");
            when(queryNormalazieService.getUserFields(any(Criteria.class))).thenReturn(mockAggregation);
            when(mongoTemplate.aggregate(eq(mockAggregation), eq("user"), eq(User.class)))
                    .thenReturn(aggregationResults);
            when(aggregationResults.getMappedResults()).thenReturn(List.of(mockUser));

            // Create a different DTO for this test to avoid conflicts
            CreateUpdatePackageDto nullInfluencerDto = new CreateUpdatePackageDto();
            nullInfluencerDto.setId("507f1f77bcf86cd799439016"); // Different ID for this test
            nullInfluencerDto.setName("Null Influencer Package");
            nullInfluencerDto.setDescription("Test Description");
            nullInfluencerDto.setStart(LocalDateTime.now().plusDays(2));
            //nullInfluencerDto.setDuration(10);

            // Mock entity mapping directly from DTO
            when(mapper.convertToEntity((CreateUpdatePackageDto) any())).thenReturn(mockPackage);
            when(mapper.convertToSubEntity((CreateUpdatePackageDto) any())).thenReturn(mockSubPackage);

            // Create a different existing package for this test
            SubPackage nullInfluencerExistingPackage = new SubPackage();
            nullInfluencerExistingPackage.setId("507f1f77bcf86cd799439016");
            nullInfluencerExistingPackage.setName("Existing Package");
            nullInfluencerExistingPackage.setPackageType(PackageType.TravelWithMe);
            nullInfluencerExistingPackage.set_package(mockPackage);
            nullInfluencerExistingPackage.setCreationDate(LocalDateTime.now().minusDays(5));
            nullInfluencerExistingPackage.setPackageStatus(com.hb.crm.core.Enums.PackageStatus.draft);
            nullInfluencerExistingPackage.setRejectionNote("");
            nullInfluencerExistingPackage.setStart(LocalDateTime.now().plusDays(1));
            nullInfluencerExistingPackage.setEnd(LocalDateTime.now().plusDays(8));
            nullInfluencerExistingPackage.setState(com.hb.crm.core.Enums.State.NotStarted);

            // Mock existing package retrieval
            when(subPackageRepository.findById("507f1f77bcf86cd799439016"))
                    .thenReturn(Optional.of(nullInfluencerExistingPackage));

            // Mock repository operations
            when(packageRepository.save(mockPackage)).thenReturn(mockPackage);
            when(subPackageRepository.save(mockSubPackage)).thenReturn(mockSubPackage);
            when(mapper.convertPackageToSearchPackage(mockSubPackage)).thenReturn(mockSearchPackage);
            when(searchPackageRepository.save(mockSearchPackage)).thenReturn(mockSearchPackage);

            // Act
            packageService.update(nullInfluencerDto);

            // Assert
            // Verify influencer fallback is set (check actual object state)
            assertEquals(mockUser, mockPackage.getInfulancer());

            // Verify all other operations proceed normally
            verify(packageRepository).save(mockPackage);
            verify(subPackageRepository).save(mockSubPackage);
            verify(searchPackageRepository).save(mockSearchPackage);
        }
    }
}
