package com.hb.crm.client.services;

import com.hb.crm.client.CommonService.Mapper;
import com.hb.crm.client.beans.UserSessionData;
import com.hb.crm.client.dto.PackageDtos.PackageDto;
import com.hb.crm.client.services.PackageServiceImpl;
import com.hb.crm.client.services.interfaces.TagService;
import com.hb.crm.core.Enums.EntityName;
import com.hb.crm.core.Enums.EntityType;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.repositories.SubPackageRepository;
import com.hb.crm.core.searchBeans.ReactionSearch;
import com.hb.crm.core.searchRepositories.ReactionSearchRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for PackageServiceImpl.GetFavouritePackages() methods.
 * Tests both overloaded methods: with and without search parameter.
 */
@ExtendWith(MockitoExtension.class)
public class PackageServiceGetFavouritePackagesTest {

    @Mock
    private SubPackageRepository subPackageRepository;

    @Mock
    private ReactionSearchRepository reactionSearchRepository;

    @Mock
    private UserSessionData userSessionData;

    @Mock
    private Mapper mapper;

    private PackageServiceImpl packageService;

    // Test data
    private String userId;
    private List<ReactionSearch> favouriteReactions;
    private List<String> packageIds;
    private List<SubPackage> mockSubPackages;
    private List<PackageDto> mockPackageDtos;
    private TagService tagService;
    @BeforeEach
    void setUp() {
        // Create a partial mock of PackageServiceImpl with only required dependencies
        packageService = spy(new PackageServiceImpl(
                null,                    // packageRepository
                null,                    // userRepository
                null,                    // subscribeRepository
                subPackageRepository,    // subPackageRepository
                null,                    // packageNotificationRepository
                null,                    // packageReactionRepository
                null,                    // mongoTemplate
                null,                    // searchmongoTemplate
                userSessionData,         // userSessionData
                null,                    // mediaService
                null,                    // activityCloneRepository
                null,                    // settingService
                null,                    // spectrum
                null,                    // paymentService
                null,                    // cashService
                null,                    // queryNormalazieService
                null,                    // atlasService
                null,                    // spectrumCommon
                null,                    // countryRepository
                null,                    // cityRepository
                null,                    // areaRepository
                null,                    // commonUtils
                mapper,                  // mapper
                null,                    // searchPackageRepository
                null,                    // activityRepository
                null,                    // activityCategoryRepository
                null,                    // searchService
                null,                    // mediaRepository
                null,                    // travelerSubscribeRepository
                reactionSearchRepository, // reactionSearchRepository
                null,                    // chatMessageService
                null,                    // groupConversationRepository
                null,                    // notificationService
                null,                    // packageNotificationSettingRepository
                null,                     // modelMapper,
                tagService
        ));

        // Setup test data
        userId = "user123";
        packageIds = Arrays.asList("pkg1", "pkg2", "pkg3");

        // Setup favourite reactions
        favouriteReactions = new ArrayList<>();
        for (String pkgId : packageIds) {
            ReactionSearch reaction = new ReactionSearch();
            reaction.setId("reaction_" + pkgId);
            reaction.setUserId(userId);
            reaction.setEntityId(pkgId);
            reaction.setEntityName(EntityName.Package);
            reaction.setEntityType(EntityType.Favourite);
            reaction.setUpdated(LocalDateTime.now());
            favouriteReactions.add(reaction);
        }

        // Setup mock SubPackages
        mockSubPackages = new ArrayList<>();
        for (String pkgId : packageIds) {
            SubPackage subPackage = new SubPackage();
            subPackage.setId(pkgId);
            subPackage.setName("Package " + pkgId);
            subPackage.setSlug("package-" + pkgId);
            subPackage.setCreationDate(LocalDateTime.now());
            mockSubPackages.add(subPackage);
        }

        // Setup mock PackageDtos
        mockPackageDtos = new ArrayList<>();
        for (String pkgId : packageIds) {
            PackageDto dto = new PackageDto();
            dto.setId(pkgId);
            dto.setName("Package " + pkgId);
            mockPackageDtos.add(dto);
        }
    }

    /**
     * Test GetFavouritePackages() without search parameter - Happy scenario
     * Should return all favourite packages when user has favourites
     */
    @Test
    void testGetFavouritePackages_NoSearch_Success() {
        // Arrange
        when(userSessionData.getId()).thenReturn(userId);
        when(reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId))
                .thenReturn(favouriteReactions);
        when(subPackageRepository.findAllById(packageIds)).thenReturn(mockSubPackages);
        
        // Mock mapper conversions
        for (int i = 0; i < mockSubPackages.size(); i++) {
            when(mapper.convertToDto(mockSubPackages.get(i))).thenReturn(mockPackageDtos.get(i));
        }

        // Act
        List<PackageDto> result = packageService.GetFavouritePackages();

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("Package pkg1", result.get(0).getName());
        assertEquals("Package pkg2", result.get(1).getName());
        assertEquals("Package pkg3", result.get(2).getName());

        // Verify method calls
        verify(userSessionData).getId();
        verify(reactionSearchRepository).findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId);
        verify(subPackageRepository).findAllById(packageIds);
        verify(mapper, times(3)).convertToDto(any(SubPackage.class));
        
        // Verify fuzzy search is NOT called
        verify(subPackageRepository, never()).findBySearchTermAndPackageIds(anyString(), anyList());
    }

    /**
     * Test GetFavouritePackages() without search parameter - No favourites scenario
     * Should return empty list when user has no favourite packages
     */
    @Test
    void testGetFavouritePackages_NoSearch_NoFavourites() {
        // Arrange
        when(userSessionData.getId()).thenReturn(userId);
        when(reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId))
                .thenReturn(new ArrayList<>());

        // Act
        List<PackageDto> result = packageService.GetFavouritePackages();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Verify method calls
        verify(userSessionData).getId();
        verify(reactionSearchRepository).findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId);
        
        // Verify repository methods are NOT called when no favourites exist
        verify(subPackageRepository, never()).findAllById(anyList());
        verify(subPackageRepository, never()).findBySearchTermAndPackageIds(anyString(), anyList());
        verify(mapper, never()).convertToDto(any(SubPackage.class));
    }

    /**
     * Test GetFavouritePackages(searchTerm) with null search term
     * Should behave same as GetFavouritePackages() without parameter
     */
    @Test
    void testGetFavouritePackages_WithNullSearchTerm_Success() {
        // Arrange
        when(userSessionData.getId()).thenReturn(userId);
        when(reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId))
                .thenReturn(favouriteReactions);
        when(subPackageRepository.findAllById(packageIds)).thenReturn(mockSubPackages);
        
        // Mock mapper conversions
        for (int i = 0; i < mockSubPackages.size(); i++) {
            when(mapper.convertToDto(mockSubPackages.get(i))).thenReturn(mockPackageDtos.get(i));
        }

        // Act
        List<PackageDto> result = packageService.GetFavouritePackages(null);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());

        // Verify method calls
        verify(userSessionData).getId();
        verify(reactionSearchRepository).findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId);
        verify(subPackageRepository).findAllById(packageIds);
        verify(mapper, times(3)).convertToDto(any(SubPackage.class));
        
        // Verify fuzzy search is NOT called
        verify(subPackageRepository, never()).findBySearchTermAndPackageIds(anyString(), anyList());
    }

    /**
     * Test GetFavouritePackages(searchTerm) with empty search term
     * Should behave same as GetFavouritePackages() without parameter
     */
    @Test
    void testGetFavouritePackages_WithEmptySearchTerm_Success() {
        // Arrange
        when(userSessionData.getId()).thenReturn(userId);
        when(reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId))
                .thenReturn(favouriteReactions);
        when(subPackageRepository.findAllById(packageIds)).thenReturn(mockSubPackages);
        
        // Mock mapper conversions
        for (int i = 0; i < mockSubPackages.size(); i++) {
            when(mapper.convertToDto(mockSubPackages.get(i))).thenReturn(mockPackageDtos.get(i));
        }

        // Act
        List<PackageDto> result = packageService.GetFavouritePackages("   ");

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());

        // Verify method calls
        verify(userSessionData).getId();
        verify(reactionSearchRepository).findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId);
        verify(subPackageRepository).findAllById(packageIds);
        verify(mapper, times(3)).convertToDto(any(SubPackage.class));
        
        // Verify fuzzy search is NOT called
        verify(subPackageRepository, never()).findBySearchTermAndPackageIds(anyString(), anyList());
    }

    /**
     * Test GetFavouritePackages(searchTerm) with valid search term - Happy scenario
     * Should use fuzzy search and return matching packages
     */
    @Test
    void testGetFavouritePackages_WithSearchTerm_Success() {
        // Arrange
        String searchTerm = "paris";
        List<SubPackage> searchResults = Arrays.asList(mockSubPackages.get(0), mockSubPackages.get(2));
        List<PackageDto> expectedDtos = Arrays.asList(mockPackageDtos.get(0), mockPackageDtos.get(2));

        when(userSessionData.getId()).thenReturn(userId);
        when(reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId))
                .thenReturn(favouriteReactions);
        when(subPackageRepository.findBySearchTermAndPackageIds(searchTerm, packageIds))
                .thenReturn(searchResults);

        // Mock mapper conversions for search results
        when(mapper.convertToDto(searchResults.get(0))).thenReturn(expectedDtos.get(0));
        when(mapper.convertToDto(searchResults.get(1))).thenReturn(expectedDtos.get(1));

        // Act
        List<PackageDto> result = packageService.GetFavouritePackages(searchTerm);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Package pkg1", result.get(0).getName());
        assertEquals("Package pkg3", result.get(1).getName());

        // Verify method calls
        verify(userSessionData).getId();
        verify(reactionSearchRepository).findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId);
        verify(subPackageRepository).findBySearchTermAndPackageIds(searchTerm, packageIds);
        verify(mapper, times(2)).convertToDto(any(SubPackage.class));

        // Verify findAllById is NOT called when search term is provided
        verify(subPackageRepository, never()).findAllById(anyList());
    }

    /**
     * Test GetFavouritePackages(searchTerm) with search term but no matches
     * Should return empty list when fuzzy search finds no matches
     */
    @Test
    void testGetFavouritePackages_WithSearchTerm_NoMatches() {
        // Arrange
        String searchTerm = "nonexistent";
        when(userSessionData.getId()).thenReturn(userId);
        when(reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId))
                .thenReturn(favouriteReactions);
        when(subPackageRepository.findBySearchTermAndPackageIds(searchTerm, packageIds))
                .thenReturn(new ArrayList<>());

        // Act
        List<PackageDto> result = packageService.GetFavouritePackages(searchTerm);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Verify method calls
        verify(userSessionData).getId();
        verify(reactionSearchRepository).findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId);
        verify(subPackageRepository).findBySearchTermAndPackageIds(searchTerm, packageIds);

        // Verify mapper is not called when no results
        verify(mapper, never()).convertToDto(any(SubPackage.class));
        verify(subPackageRepository, never()).findAllById(anyList());
    }

    /**
     * Test GetFavouritePackages(searchTerm) with search term but no favourites
     * Should return empty list when user has no favourite packages
     */
    @Test
    void testGetFavouritePackages_WithSearchTerm_NoFavourites() {
        // Arrange
        String searchTerm = "paris";
        when(userSessionData.getId()).thenReturn(userId);
        when(reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId))
                .thenReturn(new ArrayList<>());

        // Act
        List<PackageDto> result = packageService.GetFavouritePackages(searchTerm);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Verify method calls
        verify(userSessionData).getId();
        verify(reactionSearchRepository).findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId);

        // Verify search methods are NOT called when no favourites exist
        verify(subPackageRepository, never()).findBySearchTermAndPackageIds(anyString(), anyList());
        verify(subPackageRepository, never()).findAllById(anyList());
        verify(mapper, never()).convertToDto(any(SubPackage.class));
    }

    /**
     * Test GetFavouritePackages(searchTerm) with fuzzy search examples
     * Should handle typos like "pakis" → "paris" and "jahan" → "japan"
     */
    @Test
    void testGetFavouritePackages_FuzzySearchExamples() {
        // Arrange
        String searchTerm = "pakis"; // Should match "paris"

        // Create a package with "Paris" in the name
        SubPackage parisPackage = new SubPackage();
        parisPackage.setId("paris_pkg");
        parisPackage.setName("Paris Adventure");
        parisPackage.setSlug("paris-adventure");

        PackageDto parisDto = new PackageDto();
        parisDto.setId("paris_pkg");
        parisDto.setName("Paris Adventure");

        when(userSessionData.getId()).thenReturn(userId);
        when(reactionSearchRepository.findByEntityNameAndEntityTypeAndUserId(
                EntityName.Package, EntityType.Favourite, userId))
                .thenReturn(favouriteReactions);
        when(subPackageRepository.findBySearchTermAndPackageIds(searchTerm, packageIds))
                .thenReturn(Arrays.asList(parisPackage));
        when(mapper.convertToDto(parisPackage)).thenReturn(parisDto);

        // Act
        List<PackageDto> result = packageService.GetFavouritePackages(searchTerm);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Paris Adventure", result.get(0).getName());
        assertEquals("paris_pkg", result.get(0).getId());

        // Verify fuzzy search was called with correct parameters
        verify(subPackageRepository).findBySearchTermAndPackageIds(searchTerm, packageIds);
    }
}
